<?php

namespace Comave\ComaveApi\Api;

use Coditron\Lixtask\Api\Data\CustomformInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Validator\ValidateException;

/**
 * @deprecated
 */
interface LixActivityManagementInterface
{
    /**
     * Save activity data.
     *
     * @param string $activity
     * @param string $customerId
     * @return array|null
     */
    public function saveActivityData(string $activity, string $customerId): ?array;

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string Message returned by Lix api
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws ValidateException
     */
    public function rewardTaskFanRegistration(CustomerInterface $customer, ?CustomformInterface $task = null): string;

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string Message returned by Lix api
     * @throws LocalizedException When no task / wallet found
     * @throws NoSuchEntityException When referrer customer, referred customer or store is not existing anymore
     * @throws ValidateException When conditions for reward are not met or already used
     */
    public function rewardTaskReferFriend(CustomerInterface $customer, ?CustomformInterface $task = null): string;

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string Message returned by Lix api
     * @throws NoSuchEntityException
     * @throws ValidateException
     */
    public function rewardTaskProfileComplete(CustomerInterface $customer, ?CustomformInterface $task = null): string;
}
