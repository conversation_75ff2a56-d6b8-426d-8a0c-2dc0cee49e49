<?php
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use Coditron\Lixtask\Api\Data\CustomformInterface;
use Comave\ComaveApi\Api\LixActivityManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Validator\ValidateException;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;
use Magento\Newsletter\Model\Subscriber;
use Magento\Newsletter\Model\SubscriberFactory;
use Coditron\Lixtask\Api\CustomformRepositoryInterface as LixTaskRepositoryInterface;
use Magento\Customer\Model\Customer;
use Magento\Reward\Model\ResourceModel\Reward\HistoryFactory;
use Magento\Reward\Model\RewardFactory;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Comave\Referfriend\Model\Invitation;

/**
 * @deprecated
 * TODO: refactor other tasks from where they are spread
 * - 'Review On Purchased Product' - refactor from app/code/Comave/LixApiConnector/Observer/ReviewApprovalObserver.php
 * - refactor RewardSubscriber.php and OnepageControllerSuccessAction.php to use a method from this class / move all this to LixApiConnector - decide which module should be fit
 */
class LixActivityManagement implements LixActivityManagementInterface
{
    public function __construct(
        protected Customer $customerModel,
        protected \Magento\Customer\Model\ResourceModel\CustomerFactory $customerFactory,
        protected CustomerRepositoryInterface $customerRepository,
        protected CustomerCollectionFactory $customerCollectionFactory,
        protected \Comave\LixApiConnector\Helper\Data $dataHelper,
        protected LixTaskRepositoryInterface $lixTaskRepository,
        protected LoggerInterface $logger,
        protected \Magento\Store\Model\StoreManagerInterface $storeManager,
        protected RewardFactory $rewardFactory,
        protected HistoryFactory $rewardHistoryFactory,
        protected SubscriberFactory $subscriberFactory,
        protected Json $json
    ) {}

    /**
     * @param CustomerInterface $customer
     * @return string JSON activity data for usage in POST request
     */
    private function buildPostActivityJsonData(CustomerInterface $customer): string
    {
        $activityData = [];
        $activityData['phone'] = $customer->getCustomAttribute('phone_no')?->getValue();
        $activityData['email'] = $customer->getEmail();
        $activityData['name'] = $customer->getFirstname();
        return $this->json->serialize($activityData);
    }

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface $task
     * @return string
     * @throws NoSuchEntityException
     */
    private function rewardBalance(CustomerInterface $customer, CustomformInterface $task): string
    {
        $lixActivityData = $this->dataHelper->getLixActivity($task->getTaskId(), $this->buildPostActivityJsonData($customer));
        $rewardBalance = $lixActivityData['data']['coins_earned'];
        $comment = __('You earned LIX rewards on %1', ($task->getTitleShow() ?: $task->getTitle()))->render();
        $this->saveBalanceToRewardTable($rewardBalance, $customer, $comment);
        return $lixActivityData['message']; // 'Newsletter subscription activity performed successfully'
    }

    /**
     * @param string $activity
     * @param CustomerInterface $customer
     * @return CustomformInterface
     * @throws LocalizedException
     */
    protected function getTask(string $activity, CustomerInterface $customer): CustomformInterface
    {
        // determine task title prefix by activity shortcode
        $taskTitle = match ($activity) {
            'Newsletter' => 'Newsletter Subscription',
            'FanRegistration' => 'Fan Registration Reward',
            'ReferFriend' => 'Refer Friend On Comave',
            'Profile' => 'Profile Complete Reward',
            default => throw new LocalizedException(__('No task found for activity '.$activity)),
        };

        // determine task title suffix by configured wallet type
        $lixWalletAttribute = $customer->getCustomAttribute('lix_wallet');
        if (!$lixWalletAttribute) {
            throw new LocalizedException(__('No wallet attribute'));
        }
        $lixWallet = $customer->getCustomAttribute('lix_wallet')->getValue();
        $taskTitle .= match ($lixWallet) {
            $this->dataHelper->getLixcaWallet() => ' - LIXCA',
            $this->dataHelper->getLixxWallet() => ' - LIXX',
            default => throw new LocalizedException(__('Unsupported Lix wallet type')),
        };

        return $this->lixTaskRepository->getByTitle($taskTitle);
    }

    /**
     * TODO: refactor legacy multiple customer loads
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string Message returned by Lix api
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws ValidateException
     */
    public function rewardTaskFanRegistration(CustomerInterface $customer, ?CustomformInterface $task = null): string
    {
        if (!$task) {
            $task = $this->getTask('FanRegistration', $customer);
        }

        /** @noinspection SpellCheckingInspection */
        $customerClub = $customer->getCustomAttribute('customerclub');
        $customerDetails = $this->customerModel->load($customer->getId());
        $customerData = $customerDetails->getDataModel();

        if (empty($customerClub) || $customerClub->getValue() == null ) {
            throw new ValidateException(__('Customer club is not set')->render());
        }

        $fanReward = $customer->getCustomAttribute('fan_earn_reward');
        if ($fanReward != null) {
            throw new ValidateException(__('Already earned reward')->render());
        }

        $rewardResult = $this->rewardBalance($customer, $task);

        // set flags
        $customerData->setCustomAttribute('fan_earn_reward', 1);
        $customerDetails->updateData($customerData);
        $customerResource = $this->customerFactory->create();
        $customerResource->saveAttribute($customerDetails, 'fan_earn_reward');

        return $rewardResult;
    }

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string Message returned by Lix api
     * @throws LocalizedException When no task / wallet found
     * @throws NoSuchEntityException When referrer customer, referred customer or store is not existing anymore
     * @throws ValidateException When conditions for reward are not met or already used
    */
    public function rewardTaskReferFriend(CustomerInterface $customer, ?CustomformInterface $task = null): string
    {
        $referralCode = $customer->getCustomAttribute('referral_code')?->getValue();
        if (empty($referralCode)) {
            throw new ValidateException(__('No referral code found on customer')->render());
        }

        if (!$task) {
            $task = $this->getTask('ReferFriend', $customer);
        }

        // Reward referred customer (new customer)
        $referredCustomerRewardResult = $this->rewardBalance($customer, $task);

        // Reward referrer customer
        try {
            $referrerCustomerId = Invitation::getCustomerIdFromReferralCode($referralCode);
            $referrerCustomer = $this->customerRepository->getById($referrerCustomerId);
            $referrerCustomerRewardResult = $this->rewardBalance($referrerCustomer, $task);
        } catch (NoSuchEntityException) {
            throw new NoSuchEntityException(__('Referrer customer not found'));
        }

        return 'Referred: ' . $referredCustomerRewardResult . ', Referrer: ' . $referrerCustomerRewardResult;
    }

    /**
     * @param CustomerInterface $customer
     * @param CustomformInterface|null $task
     * @return string
     * @throws NoSuchEntityException
     * @throws ValidateException
     */
    public function rewardTaskProfileComplete(CustomerInterface $customer, ?CustomformInterface $task = null): string
    {
        if (!$task) {
            $task = $this->getTask('Profile', $customer);
        }

        $customerDetails = $this->customerModel->load($customer->getId());
        $customerData = $customerDetails->getDataModel();

        $profileReward = $customer->getCustomAttribute('profile_reward');
        if ($profileReward != null) {
            throw new ValidateException('Already earned reward');
        }

        $rewardResult = $this->rewardBalance($customer, $task);

        // set flags
        $customerData->setCustomAttribute('profile_reward', 1);
        $customerDetails->updateData($customerData);
        $customerResource = $this->customerFactory->create();
        $customerResource->saveAttribute($customerDetails, 'profile_reward');

        return $rewardResult;
    }

    /**
     * @param string $activity
     * @param string $customerId
     * @return array[]|void
     * @throws \Zend_Log_Exception
     *@deprecated Used by rest API, which is now disabled
     */
    public function saveActivityData(string $activity, string $customerId): ?array
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/lixactivity.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (NoSuchEntityException|LocalizedException) {
            return ['data' => ['success' => false, 'message' => __('Error loading customer with ID %1', $customerId)]];
        }

        try {
            $task = $this->getTask($activity, $customer);
        } catch (NoSuchEntityException|LocalizedException $e) {
            return ['data' => ['success' => false, 'message' => $e->getMessage()]];
        }

        try {
            $message = match ($activity) {
                'Newsletter' => "0", // @TODO: remove
                'FanRegistration' => $this->rewardTaskFanRegistration($customer, $task),
                'ReferFriend' => $this->rewardTaskReferFriend($customer, $task),
                'Profile' => $this->rewardTaskProfileComplete($customer, $task),
                default => throw new LocalizedException(__('No task found for activity '.$activity)),
            };

            return ['data' => ['success' => true, 'message' => $message]];
        } catch (ValidateException $e) {
            return ['data' => ['success' => false, 'message' => $e->getMessage()]];
        } catch (\Exception $e) {
                $responseData['status'] = false;
                $responseData['message'] = 'Insufficient balance or another error occurred';
                $this->logger->error($responseData['message'] . ' - ' . $e->getMessage());
                return ['data' => $responseData];
        }
    }

    /**
     * @throws NoSuchEntityException
     * @throws \Exception
     */
    protected function saveBalanceToRewardTable($rewardBalance, CustomerInterface $customer, ?string $comment = null): void
    {
        $store = $this->storeManager->getStore();
        $websiteId = $store->getWebsiteId();

        $reward = $this->rewardFactory->create()->setCustomer($customer)
            ->setCustomerId($customer->getId())
            ->setWebsiteId($websiteId)
            ->setPointsDelta($rewardBalance)
            ->setAction(\Magento\Reward\Model\Reward::REWARD_ACTION_ADMIN)
            ->setComment($comment)
            ->updateRewardPoints();
        $reward->save();
    }
}
