<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Controller\Adminhtml\Export;

use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Backend\App\Action;
use Magento\Framework\App\Response\Http\FileFactory;
use Comave\CategoryCommission\Model\CommissionRepository;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Convert\ConvertArray;
use Magento\Framework\App\ResponseInterface;
use Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterface;

class Download extends Action
{

    /**
     * @param Context $context
     * @param Filesystem $filesystem
     * @param FileFactory $fileFactory
     * @param CommissionRepository $commissionRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Filesystem\Io\File $file
     * @param ConvertArray $convertArray
     */
    public function __construct(
                   Context $context,
        private    Filesystem $filesystem,
        private    FileFactory $fileFactory,
        private    CommissionRepository $commissionRepository,
        private    SearchCriteriaBuilder $searchCriteriaBuilder,
        private    File $file,
        private    ConvertArray $convertArray
    ) {
        parent::__construct($context);
    }

    /**
     * @return ResponseInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $params = $this->getRequest()->getParams();
        $header = ['entity_id', 'category_id', 'category_name', 'store_id', 'type', 'value'];
        $input = $this->commissionRepository->getList($this->searchCriteriaBuilder->create());

        if(isset($params['csv'])){
            return $this->generateCSV($header, $input);
        }else{
            return $this->generateXml($header, $input);
        }
    }

    /**
     * @param array $header
     * @param \Magento\Framework\Api\SearchResults $data
     * @return ResponseInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    private function generateCSV(array $header, \Magento\Framework\Api\SearchResults $data): ResponseInterface
    {
        $filepath = 'export/commissions.csv';
        $downloadedFileName = 'CategoryCommissions.csv';
        $directory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $directory->create('export');
        $stream = $directory->openFile($filepath, 'w+');
        $stream->lock();
        $stream->writeCsv($header);
        foreach ($data->getItems() as $line) {
            $data = [];
            $data[] = $line['entity_id'];
            $data[] = $line['category_id'];
            $data[] = $line['category_name'];
            $data[] = $line['store_id'];
            $data[] = $line['type'];
            $data[] = $line['value'];
            $stream->writeCsv($data);
        }
        $content['type'] = 'filename';
        $content['value'] = $filepath;
        $content['rm'] = 1;

        return $this->fileFactory->create($downloadedFileName, $content, DirectoryList::VAR_DIR);
    }

    /**
     * @param array $header
     * @param \Magento\Framework\Api\SearchResults $data
     * @return ResponseInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function generateXml(array $header, \Magento\Framework\Api\SearchResults $data): ResponseInterface
    {
        $content = [];
        foreach ($data->getItems() as $item) {
            $associatedData = [];
            $associatedData['entity_id'] = $item['entity_id'];
            $associatedData['category_id'] = $item['category_id'];
            $associatedData['category_name'] = $item['category_name'];
            $associatedData['store_id'] = $item['store_id'];
            $associatedData['type'] = $item['type'];
            $associatedData['value'] = $item['value'];
            $content["rule_".$item['entity_id']][] = $associatedData;
        }
        // ConvertArray function assocToXml to create xmlContents
        $xmlContents = $this->convertArray->assocToXml($content, "categoryCommission");
        // convert it to xml using asXML() function
        $content = $xmlContents->asXML();
        $downloadedFileName = 'CategoryCommissions.xml';
        return $this->fileFactory->create($downloadedFileName, $content, DirectoryList::VAR_DIR);
    }
}
