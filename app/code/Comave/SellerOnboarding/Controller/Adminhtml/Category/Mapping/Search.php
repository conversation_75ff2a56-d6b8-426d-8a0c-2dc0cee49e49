<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Controller\Adminhtml\Category\Mapping;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Catalog\Model\ResourceModel\Category\Collection;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;

class Search extends Action implements HttpGetActionInterface
{
    /**
     * @param Context $context
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        private readonly CollectionFactory $collectionFactory
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Json
    {
        $searchTerm = filter_var(
            $this->getRequest()->getParam('q') ?? '',
            FILTER_UNSAFE_RAW
        );

        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $collection->addAttributeToFilter(
            'name',
            ['like' => '%' . strip_tags($searchTerm) . '%']
        );

        $result = [
            'items' => [],
            'total_pages' => 1,
            'page' => 1
        ];

        if (!$collection->getSize()) {
            return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
        }

        foreach ($collection->getItems() as $category) {
            $result['items'][] = [
                'id' => $category->getId(),
                'text' => $category->getName()
            ];
        }

        return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
    }
}
