<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Controller\Adminhtml\Category\Mapping;

use Comave\SellerOnboarding\Api\Category\MappingRepositoryInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;

class Assign extends Action implements HttpGetActionInterface, HttpPostActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_SellerOnboarding::category_mapping_list';

    /**
     * @param Context $context
     * @param MappingRepositoryInterface $mappingRepository
     */
    public function __construct(
        Context $context,
        private readonly MappingRepositoryInterface $mappingRepository
    ) {
        parent::__construct($context);
    }

    /**
     * @return Redirect|Json
     */
    public function execute(): Redirect|Json
    {
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setRefererUrl();
        /** @var string|null $mappingId */
        $mappingId = $this->getRequest()->getParam('mapping_id');

        if (empty($mappingId)) {
            $this->getMessageManager()->addErrorMessage(
                __('Unable to process mapping, invalid request')
            );
            return $resultRedirect;
        }

        $withError = false;

        try {
            $categoryIds = $this->getRequest()->getParam('category_ids', []);
            $mapping = $this->mappingRepository->get((int) $mappingId);

            if (in_array($mapping->getMappingCategoryId(), $categoryIds)) {
                unset($categoryIds[array_search($mapping->getMappingCategoryId(), $categoryIds)]);
            }

            $mapping->setMappingType(MappingInterface::MAPPING_TYPE_MANUAL)
                ->setMappingAccuracy(100.00);
            $this->mappingRepository->save($mapping);

            if (!empty($categoryIds)) {
                $this->processOtherCategories($mapping, $categoryIds);
            }

            if (!$this->getRequest()->isAjax()) {
                $this->getMessageManager()->addSuccessMessage(
                    __('Successfully assigned category manually')
                );
            }
        } catch (\Exception $e) {
            $withError = true;
            $this->getMessageManager()->addErrorMessage(
                $e->getMessage()
            );
        }

        return $this->getRequest()->isAjax() ?
            $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData([
                'success' => !$withError
            ]) : $resultRedirect;
    }

    /**
     * @param MappingInterface $mapping
     * @param array $categoryIds
     * @return void
     */
    private function processOtherCategories(MappingInterface $mapping, array $categoryIds): void
    {
        foreach ($categoryIds as $categoryId) {
            $newMapping = clone $mapping;
            $newMapping->unsetId();
            $newMapping->setMappingCategoryId((int) $categoryId)
                ->setMappingAccuracy(100.00)
                ->setMappingType(MappingInterface::MAPPING_TYPE_MANUAL);
            $this->mappingRepository->save($newMapping);
        }
    }
}
