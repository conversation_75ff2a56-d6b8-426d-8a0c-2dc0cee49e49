<?php

declare(strict_types=1);

namespace Comave\LixApi\Model\Command;

use Comave\ComaveApi\Api\LixActivityManagementInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Psr\Log\LoggerInterface;

class LixTasksHandler
{
    /**
     * @param LixActivityManagementInterface $lixActivityManagement
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly LixActivityManagementInterface $lixActivityManagement,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param CustomerInterface $customer
     * @return bool
     */
    public function handle(CustomerInterface $customer): bool
    {
        try {
            $message = $this->lixActivityManagement->rewardTaskReferFriend($customer);
            $this->logger->info(
                '[LixTaskRegisterHandler] Processing refer a friend successful',
                [
                    'message' => $message
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                '[LixTaskRegisterHandler] Unable to process refer a friend',
                [
                    'message' => $e->getMessage(),
                    'customer' => $customer->getEmail()
                ]
            );
        }

        try {
            $message = $this->lixActivityManagement->rewardTaskFanRegistration($customer);
            $this->logger->info(
                '[LixTaskRegisterHandler] Processing fan registration successful',
                [
                    'message' => $message
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                '[LixTaskRegisterHandler] Unable to process fan registration',
                [
                    'message' => $e->getMessage(),
                    'customer' => $customer->getEmail()
                ]
            );
        }

        return true;
    }
}
