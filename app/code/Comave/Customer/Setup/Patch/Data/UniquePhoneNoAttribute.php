<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use <PERSON>gento\Customer\Setup\CustomerSetupFactory;
use <PERSON>gento\Customer\Model\Customer;

/**
 * Data patch that marks the customer attribute `phone_no` as unique.
 *
 */
class UniquePhoneNoAttribute implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup  Setup interface for DB operations
     * @param CustomerSetupFactory     $customerSetupFactory  Factory to create CustomerSetup instances
     */
    public function __construct(
        private ModuleDataSetupInterface $moduleDataSetup,
        private CustomerSetupFactory     $customerSetupFactory
    ) {
    }

    /**
     * Apply the patch.
     *
     * Looks up the `phone_no` attribute on the Customer entity and sets
     * its `is_unique` flag so Magento enforces unique values.
     *
     * @throws \RuntimeException If the attribute does not already exist
     * @return $this
     */
    public function apply(): self
    {
        $customerSetup = $this->customerSetupFactory->create(
            ['setup' => $this->moduleDataSetup]
        );

        $attribute = $customerSetup->getAttribute(Customer::ENTITY, 'phone_no');
        if (!$attribute) {
            throw new \RuntimeException("'phone_no' attribute does not exist");
        }

        $customerSetup->updateAttribute(
            Customer::ENTITY,
            'phone_no',
            ['is_unique' => 1]
        );

        return $this;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
