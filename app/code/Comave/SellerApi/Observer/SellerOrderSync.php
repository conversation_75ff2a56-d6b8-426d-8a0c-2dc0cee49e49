<?php

declare(strict_types=1);

namespace Comave\SellerApi\Observer;

use Comave\SellerApi\Api\OrderSyncMessageInterface;
use Comave\SellerApi\Api\OrderSyncMessageInterfaceFactory;
use Comave\SellerApi\Model\Queue\Consumer\OrderSync;
use Comave\SellerApi\Service\OrderSellersIdentifier;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\Invoice;
use Psr\Log\LoggerInterface;

class SellerOrderSync implements ObserverInterface
{
    private array $dispatched = [];
    private array $processed = [];

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param EncryptorInterface $encryptor
     * @param SerializerInterface $serializer
     * @param PublisherInterface $publisher
     * @param ManagerInterface $eventManager
     * @param OrderSyncMessageInterfaceFactory $messageFactory
     * @param LoggerInterface $logger
     * @param OrderSellersIdentifier $orderSellersIdentifier
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly EncryptorInterface $encryptor,
        private readonly SerializerInterface $serializer,
        private readonly PublisherInterface $publisher,
        private readonly ManagerInterface $eventManager,
        private readonly OrderSyncMessageInterfaceFactory $messageFactory,
        private readonly LoggerInterface $logger,
        private readonly OrderSellersIdentifier $orderSellersIdentifier,
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $invoice = $observer->getEvent()->getInvoice();

        if (in_array($invoice->getId(), $this->processed)) {
            return;
        }

        $isPaid = $invoice->getIsPaid() || $invoice->getState() === Invoice::STATE_PAID;

        if (!$invoice instanceof Invoice || !$isPaid) {
            $this->logger->error(
                '[SellerOrderSync] Unable to locate order, no invoice found / not paid',
                [
                    'eventData' => $observer->getData(),
                    'invoice' => $isPaid,
                ]
            );

            return;
        }

        if ($invoice->getState() === \Magento\Sales\Model\Order\Invoice::STATE_PAID
            && $invoice->getOrigData('state') === \Magento\Sales\Model\Order\Invoice::STATE_PAID) {
            $this->processed[] = $invoice->getId();

            return;
        }


        try {
            if (empty($invoice->getOrderId()) && !$invoice->getOrder()) {
                throw new NoSuchEntityException(
                    __('Missing order id on invoice')
                );
            }

            $order = $invoice->getOrder() instanceof OrderInterface ?
                $invoice->getOrder() : (
                    !empty($invoice->getOrderId()) ?
                        $this->orderRepository->get($invoice->getOrderId()) :
                        null
                );
        } catch (NoSuchEntityException) {
            $this->logger->error(
                '[SellerOrderSync] Unable to locate order',
                [
                    'eventData' => $invoice->getOrderId()
                ]
            );

            return;
        }

        if (empty($order) || !$order->getEntityId()) {
            $this->logger->error(
                '[SellerOrderSync] Unable to locate order',
                [
                    'eventData' => $invoice->getOrderId()
                ]
            );

            return;
        }

        $sellerItems = $this->orderSellersIdentifier->get($order);
        if (empty($sellerItems)) {
            $this->logger->error(
                '[SellerOrderSync] Unable to process order, no seller products detected',
                [
                    'order' => $order->getIncrementId()
                ]
            );

            return;
        }

        foreach ($sellerItems as $sellerIdentifier => $items) {
            if (empty($items)) {
                continue;
            }

            $publishHash = $this->getDispatchKey($sellerIdentifier, implode(',', $items));

            if (in_array($publishHash, $this->dispatched)) {
                $this->logger->warning(
                    '[SellerOrderSync] Queue sync order already dispatched',
                    [
                        'order' => $order->getEntityId(),
                        'sellerIdentifier' => $sellerIdentifier,
                        'items' => $items
                    ]
                );

                continue;
            }

            $this->dispatched[] = $publishHash;
            $this->processed[] = $invoice->getId();
            /** @var OrderSyncMessageInterface $message */
            $message = $this->messageFactory->create();
            $message->setSellerIdentifier($sellerIdentifier)
                ->setOrderId((string) $order->getEntityId())
                ->setItems($this->serializer->serialize($items));

            $this->eventManager->dispatch(
                'before_order_sync_queue_dispatch',
                [
                    'message' => $message,
                    'order' => $order,
                ]
            );

            $this->publisher->publish(
                OrderSync::TOPIC_NAME,
                $message
            );

            $this->logger->info(
                '[SellerOrderSync] Published queue sync order',
                [
                    'order' => $order->getEntityId(),
                    'sellerIdentifier' => $sellerIdentifier,
                    'items' => $items
                ]
            );
        }
    }

    /**
     * @param string $sellerIdentifier
     * @param string $itemIds
     * @return string
     */
    private function getDispatchKey(string $sellerIdentifier, string $itemIds): string
    {
        return $this->encryptor->encrypt(
            $sellerIdentifier . $itemIds
        );
    }
}
