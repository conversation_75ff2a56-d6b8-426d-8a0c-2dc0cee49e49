<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Webhook\Processors;

use Comave\SellerApi\Model\IntegrationTypePool;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface;
use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Comave\ShopifyAccounts\Model\Command\GetSellerByDomain;
use Comave\ShopifyAccounts\Model\Command\TmpDeleteItems;
use Comave\ShopifyAccounts\Model\Command\Rest\FormatImportData;
use Comave\ShopifyAccounts\Model\Command\Rest\ShopifyToMagentoMapper;
use Comave\ShopifyAccounts\Model\Queue\Consumer\ProcessCategoryMapping;
use Magento\Framework\App\HttpRequestInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Registry;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class ProductUpdate implements WebhookTopicProcessorInterface
{
    /**
     * @param Registry $coreRegistry
     * @param SerializerInterface $serializer
     * @param Emulation $emulation
     * @param FormatImportData $formatImportData
     * @param StoreManagerInterface $storeManager
     * @param ShopifyToMagentoMapper $shopifyToMagentoMapper
     * @param LoggerInterface $logger
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     * @param TmpDeleteItems $tmpDeleteItems
     * @param PublisherInterface $publisher
     * @param IntegrationTypePool $integrationTypePool
     * @param ResourceConnection $resourceConnection
     * @param GetSellerByDomain $getSellerByDomain
     */
    public function __construct(
        private readonly Registry $coreRegistry,
        private readonly SerializerInterface $serializer,
        private readonly Emulation $emulation,
        private readonly FormatImportData $formatImportData,
        private readonly StoreManagerInterface $storeManager,
        private readonly ShopifyToMagentoMapper $shopifyToMagentoMapper,
        private readonly LoggerInterface $logger,
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
        private readonly TmpDeleteItems $tmpDeleteItems,
        private readonly PublisherInterface $publisher,
        private readonly IntegrationTypePool $integrationTypePool,
        private readonly ResourceConnection $resourceConnection,
        private readonly GetSellerByDomain $getSellerByDomain
    ) {
    }

    /**
     * @param HttpRequestInterface $request
     * @return void
     * @throws InvalidWebhookRequestException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function process(HttpRequestInterface $request): void
    {
        $accountData = $this->getSellerByDomain->get(
            $request->getHeader(WebhookValidatorInterface::SELLER_DOMAIN_HEADER)
        );

        if (empty($accountData)) {
            throw new InvalidWebhookRequestException(__('Unable to identify seller'));
        }

        $accountId = $accountData['entity_id'];
        $json = $request->getContent();
        $productsList = [$this->serializer->unserialize($json)];

        array_map(function ($product) use (&$tmpData, $accountId) {
            $tmpData[] = [
                'item_type' => 'product',
                'item_id' => $product['id'],
                'product_data' => $this->serializer->serialize($product),
                'associate_products' => $this->serializer->serialize(
                    $product['variants'] ?? []
                ),
                'rule_id' => $accountId,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }, $productsList);

        $connection = $this->resourceConnection->getConnection();
        $connection->delete(
            $connection->getTableName('wk_mpmultishopify_tempshopify'),
            ["rule_id = ?" => $accountId]
        );
        $connection->insertMultiple(
            $connection->getTableName('wk_mpmultishopify_tempshopify'),
            $tmpData
        );

        $this->processProduct($accountId);
    }

    /**
     * @param string $shopifyAccountId
     * @return void
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function processProduct(string $shopifyAccountId): void
    {
        try {
            $this->coreRegistry->register('isSecureArea', 1);
            $this->storeManager->setCurrentStore(0);
            $this->emulation->startEnvironmentEmulation(0, \Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception) { //phpcs:ignore
        }

        $magentoProducts = $this->formatImportData->execute($shopifyAccountId);
        foreach ($magentoProducts as $product) {
            try {
                $product->save();
                $this->logger->info(
                    '[WebhookProcessor] Processed product update successfully',
                    [
                        'processor' => __CLASS__,
                        'sku' => $product->getSku(),
                        'accountId' => $shopifyAccountId
                    ]
                );
            } catch (\Exception $e) {
                $this->logger->error(
                    '[WebhookProcessor] Failed processing product',
                    [
                        'message' => $e->getMessage(),
                        'accountId' => $shopifyAccountId
                    ]
                );
                continue;
            }
        }

        $this->mediaGalleryRegistry->process();
        $this->shopifyToMagentoMapper->processMapping();
        $this->tmpDeleteItems->delete();
        $this->publisher->publish(
            ProcessCategoryMapping::TOPIC_NAME,
            $shopifyAccountId
        );
        $this->integrationTypePool->populate('shopify');

        try {
            $this->emulation->stopEnvironmentEmulation();
            $this->coreRegistry->unregister('isSecureArea');
        } catch (\Exception) {//phpcs:ignore
        }
    }
}
