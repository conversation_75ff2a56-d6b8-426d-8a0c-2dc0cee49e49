<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\CategoryMatcher\Model\ConfigProvider;
use Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Magento\Framework\App\ResourceConnection;

class CategoryMapper
{
    private ?array $loadedCategories = [];

    /**
     * @param ConfigProvider $configProvider
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param string $productSku
     * @param string $shopifyCategoryName
     * @param string $shopifyAccountId
     * @param string $sellerId
     * @param array|null $productData
     * @return string[]|null
     */
    public function getByShopifyName(
        string $productSku,
        string $shopifyCategoryName,
        string $shopifyAccountId,
        string $sellerId,
        ?array $productData = []
    ): ?array {
        $connection = $this->resourceConnection->getConnection();

        if (empty($this->loadedCategories)) {
            $categoriesSelect = $connection->select()
                ->from(
                    ['m' => $connection->getTableName('comave_seller_onboarding_category_mapping')],
                    []
                )->join(
                    ['mp' => $connection->getTableName('comave_seller_onboarding_category_data_source')],
                    'm.mapping_source_id = mp.source_id',
                    [
                        DataSourceInterface::SOURCE_CATEGORY_NAME,
                        'category_id' => new \Zend_Db_Expr('GROUP_CONCAT(mapping_category_id)')
                    ]
                )->where(
                    'seller_id = ?',
                    $sellerId
                )->where(
                    'mapping_type = ?',
                    MappingInterface::MAPPING_TYPE_MANUAL
                )->group(
                    MappingInterface::MAPPING_SOURCE_ID
                );

            $this->loadedCategories = $connection->fetchAssoc($categoriesSelect);
        }

        $currentCategory = $this->loadedCategories[$shopifyCategoryName]['category_id'] ?? null;
        if ($currentCategory === null && $this->configProvider->isEnabled() && !empty($shopifyCategoryName)) {
            $connection->insert(
                $connection->getTableName('comave_shopify_category_matcher'),
                [
                    'sku' => $productSku,
                    'word' => $shopifyCategoryName,
                    'account_id' => $shopifyAccountId
                ],
            );

            if ($connection->isTableExists('comave_seller_onboarding_category_data_source')) {
                //These are specifically added because these are not actually categories but rather matched against product_type
                $connection->insertOnDuplicate(
                    $connection->getTableName('comave_seller_onboarding_category_data_source'),
                    [
                        'seller_id' => $sellerId,
                        'source_category_id' => md5($shopifyCategoryName),
                        'source_category_name' => $shopifyCategoryName,
                        'source_metadata' => json_encode($productData)
                    ],
                    [
                        'source_category_id',
                        'source_metadata',
                        'source_category_name'
                    ]
                );
            }
        }

        return $currentCategory !== null ? explode(',', $currentCategory) : null;
    }
}
