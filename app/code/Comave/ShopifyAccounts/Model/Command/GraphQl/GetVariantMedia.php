<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command\GraphQl;

use Comave\SellerApi\Service\RequestHandler;
use Comave\ShopifyAccounts\Model\Command\GetVariantMediaGraphqlString;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Log\LoggerInterface;

class GetVariantMedia
{
    /**
     * @param GetVariantMediaGraphqlString $getVariantMediaGraphqlString
     * @param RequestHandler $requestHandler
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly GetVariantMediaGraphqlString $getVariantMediaGraphqlString,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param string $shopifyAccountId
     * @param string $shopifyProductId
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function get(string $shopifyAccountId, string $shopifyProductId): array
    {
        $queryJson = json_encode([
            'query' => trim($this->getVariantMediaGraphqlString->get($shopifyProductId))
        ]);
        $configurableApi = $this->configurableApiFactory->create([
            'shopifyAccountId' => $shopifyAccountId,
        ])->setEndpoint(ConfigurableApi::GRAPHQL)
            ->setParams($queryJson)
            ->setMethod('POST');

        try {
            $response = $this->requestHandler->handleRequest($configurableApi);

            if ($response->hasError()) {
                throw new \Exception($response->getResult()->getReasonPhrase());
            }

            $responseResult = $response->getResult();
            $decodedResponse = $this->serializer->unserialize(
                $responseResult->getBody()->getContents()
            );

            if (!empty($decodedResponse['errors'])) {
                throw new \Exception(implode(', ', $decodedResponse['errors']));
            }

            $mediaEntries = [
                'images' => []
            ];

            foreach ($decodedResponse['data']['product']['variants']['edges'] as $variant) {
                if (empty($variant['node']['media']['edges'])) {
                    continue;
                }

                foreach ($variant['node']['media']['edges'] as $mediaNode) {
                    if ($mediaNode['node']['mediaContentType'] !== 'IMAGE') {
                        continue;
                    }

                    $mediaEntries['images'][] = [
                        'variant_ids' => [
                            (string) preg_replace("/[^0-9]/", '', $variant['node']['id'])
                        ],
                        'src' => $mediaNode['node']['preview']['image']['url']
                    ];
                }
            }

            return empty($mediaEntries['images']) ? [] : $mediaEntries;
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ShopifyAccountVariantMediaGraphQl] Unable to retrieve media for variants',
                [
                    'message' => $e->getMessage(),
                ]
            );

            return [];
        }
    }
}
