<?php

namespace Comave\Marketplace\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as MpProductCollection;
use Comave\Marketplace\Model\Indexer\ThresholdIndexer;

class SellerChangedLowThreshold implements ObserverInterface
{
    /**
     * @param MpProductCollection $mpProductCollection
     * @param ThresholdIndexer $thresholdIndexer
     */
    public function __construct(
       private readonly MpProductCollection $mpProductCollection,
       private readonly ThresholdIndexer $thresholdIndexer
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws \Exception
     */
    public function execute(Observer $observer): void
    {
        $seller = $observer->getEvent()->getSeller();
        $sellerProducts = $this->mpProductCollection->create()->addFieldToFilter('seller_id', $seller->getEntityId());
        $productIds = [];
        foreach ($sellerProducts as $product) {
            $productIds[] = $product->getData('mageproduct_id');
        }
        $this->thresholdIndexer->executeList($productIds);
    }
}
