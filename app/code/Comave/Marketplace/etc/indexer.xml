<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Indexer:etc/indexer.xsd">
    <indexer id="threshold_indexer"
             view_id="threshold_indexer"
             class="Comave\Marketplace\Model\Indexer\ThresholdIndexer"
             shared_index="threshold_indexer">
        <title>Low Stock Threshold Indexer</title>
        <description>Indexes low stock threshold to OpenSearch</description>
        <dependencies>
            <indexer id="catalog_product_attribute" />
        </dependencies>
    </indexer>
</config>
