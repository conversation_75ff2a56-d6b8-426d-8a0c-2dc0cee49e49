<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Mail\Template\TransportBuilder">
        <plugin name="comave_marketplace_forgot_email_transport_builder_plugin" type="Comave\Marketplace\Plugin\TransportBuilderPlugin" sortOrder="10" />
    </type>
    <type name="Magento\Customer\Controller\Account\ResetPasswordPost">
        <plugin name="comave_marketplace_reset_password_post_plugin"
                type="Comave\Marketplace\Plugin\ResetPasswordPostPlugin"
                sortOrder="10" />
    </type>
    <preference for="Comave\Marketplace\Api\Url\FrontendUrlInterface" type="Comave\Marketplace\Model\Url\FrontendUrl" />

    <type name="Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\Product\CompositeCollectionProcessor">
        <arguments>
            <argument name="collectionProcessors" xsi:type="array">
                <item name="marketplaceApproval" xsi:type="object">Comave\Marketplace\Model\Resolver\CollectionProcessor\ApprovedProducts</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Elasticsearch\Model\Adapter\FieldMapper\FieldMapperResolver">
        <arguments>
            <argument name="fieldMappers" xsi:type="array">
                <item name="threshold_indexer" xsi:type="string">Magento\Elasticsearch\Elasticsearch5\Model\Adapter\FieldMapper\ProductFieldMapperProxy</item>
            </argument>
        </arguments>
    </type>

</config>
