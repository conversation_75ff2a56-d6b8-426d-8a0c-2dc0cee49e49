<?php
declare(strict_types=1);

namespace Comave\Marketplace\Model\Service;

use Comave\Marketplace\Model\Indexer\ThresholdIndexer;
use Magento\AdvancedSearch\Model\Client\ClientResolver;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Elasticsearch\Model\Adapter\Index\IndexNameResolver;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\Phrase;

class GetProductQtyThresholdNotice
{

    /**
     * @param LoggerInterface $logger
     * @param ClientResolver $searchClientResolver
     * @param CollectionFactory $collectionFactory
     * @param IndexNameResolver $indexNameResolver
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ClientResolver $searchClientResolver,
        private readonly CollectionFactory $collectionFactory,
        private readonly IndexNameResolver $indexNameResolver,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    /**
     * @param int $entityId
     * @return Phrase|string
     * @throws LocalizedException
     */
    public function getProductQtyThresholdNotice(int $entityId): Phrase|string
    {
        if (empty($entityId)) {
            return "";
        }
        $storeId = (int)$this->storeManager->getDefaultStoreView()->getId();
        $indexName = $this->indexNameResolver->getIndexNameForAlias(
            $storeId,
            ThresholdIndexer::INDEXER_NAME
        );
        $query = [
            'index' => $indexName,
            'body' => [
                'query' => [
                    'prefix' => [
                        'entity_id' => $entityId
                    ],
                ],
                'size' => 10,
            ]
        ];
        try {
            $response = $this->searchClientResolver->create()->query($query);
        } catch (\Exception $e) {
            $this->logger->warning(
                '[Low Stock Notice] Opensearch error trying to retrieve stock info',
                [
                    'id' => $entityId,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTrace()
                ]
            );
            throw new LocalizedException(__('Unable to retrieve es stock notice info'));
        }
        if (empty($response['hits']['hits'])) {
            $this->logger->info(
                '[Low Stock Notice] No low stock notice found for entity_id',
                [
                    'entity_id' => $entityId,
                ]
            );
            return "";
        }

        $tresholdValue = $response['hits']['hits'][0]['_source']['seller_low_stock_threshold_grid'];

        if (empty($tresholdValue)) {
            $this->logger->info(
                '[Low Stock Notice] No low stock notice found for entity_id, only item with specific entity_id',
                [
                    'entity_id' => $entityId,
                ]
            );
            return "";
        }
        return __($tresholdValue);
    }
}
