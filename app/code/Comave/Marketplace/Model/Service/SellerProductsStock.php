<?php
declare(strict_types=1);

namespace Comave\Marketplace\Model\Service;

use Comave\Marketplace\Model\Config\ConfigurationProvider;
use Magento\Customer\Model\SessionFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as MpProductCollection;
use Webkul\Marketplace\Model\ResourceModel\Product\Collection;
use Magento\Framework\Phrase;

class SellerProductsStock
{

    private $cache = [];

    public const STOCK_THRESHOLD_VALUES = ['out_of_stock' => 'Out Of Stock', 'low_stock' => 'Low Stock', 'in_stock' => 'In Stock'];

    /**
     * @param ConfigurationProvider $configurationProvider
     * @param SessionFactory $sessionFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(
        private ConfigurationProvider $configurationProvider,
        private SessionFactory $sessionFactory,
        private CustomerRepositoryInterface $customerRepository,
        private MpProductCollection $mpProductCollection
    ) {
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomer(): \Magento\Customer\Model\Customer
    {
        return $this->sessionFactory->create()->getCustomer();
    }

    /**
     * @param $productId
     * @return int
     */
    public function getSellerLowStockTreshold($productId = null): int
    {
        if (!$productId) {
            $threshold = $this->getCustomer()->getSellerLowStockThresholdGrid();
            if ($threshold) {
                return (int)$threshold;
            }
        } else {
            if (isset($this->cache[$productId])) {
                return $this->cache[$productId];
            }
            // admin use should check the product seller for overwrite of admin threshold before loading admin config
            $marketplaceProduct = $this->getSellerProductDataByProductId($productId)->getFirstItem();
            $sellerId = $marketplaceProduct->getData('seller_id');
            if (!empty($sellerId)) {
                try {
                    $customer = $this->customerRepository->getById($sellerId);
                } catch (NoSuchEntityException|LocalizedException $e) {
                    $customer = null;
                }

                if ($customer) {
                    $sellerThreshold = $customer->getCustomAttribute('seller_low_stock_threshold_grid');
                    if ($sellerThreshold) {
                        $this->cache[$productId] = (int)$sellerThreshold->getValue();
                        return (int)$sellerThreshold->getValue();
                    }
                }
            }
        }

        return $this->configurationProvider->getAdminStockThreshold();
    }

    /**
     * Return the seller product data by product id.
     *
     * @param int $productId
     * @return Collection
     */
    private function getSellerProductDataByProductId($productId = ''): Collection
    {
        $collection = $this->mpProductCollection->create();
        return $collection->addFieldToFilter('mageproduct_id', $productId);
    }

    /**
     * @param int|float $productQty
     * @param int $threshold
     * @return Phrase
     */
    public function getStockStatus(int|float $productQty, int $threshold): Phrase
    {
        if ($productQty <= $threshold && $productQty > 0) {
            return __(SellerProductsStock::STOCK_THRESHOLD_VALUES['low_stock']);
        }
        if ($productQty > $threshold) {
            return __(SellerProductsStock::STOCK_THRESHOLD_VALUES['in_stock']);
        }
        return __(SellerProductsStock::STOCK_THRESHOLD_VALUES['out_of_stock']);
    }
}
