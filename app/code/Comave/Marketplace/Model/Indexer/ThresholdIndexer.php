<?php
declare(strict_types=1);

namespace Comave\Marketplace\Model\Indexer;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\DB\Select;
use Magento\Framework\Indexer\ActionInterface as IndexerActionInterface;
use Magento\Framework\Mview\ActionInterface as MviewActionInterface;
use Magento\Elasticsearch\Model\Adapter\Elasticsearch;
use Magento\Store\Model\StoreManagerInterface;
use Magento\InventorySalesAdminUi\Model\GetSalableQuantityDataBySku;
use Magento\Store\Model\Store;
use Comave\Marketplace\Model\Service\SellerProductsStock;
use Psr\Log\LoggerInterface;

class ThresholdIndexer implements IndexerActionInterface, MviewActionInterface
{
    public const string INDEXER_NAME = 'threshold_indexer';

    /**
     * @param StoreManagerInterface $storeManager
     * @param Elasticsearch $esAdapter
     * @param CollectionFactory $collectionFactory
     * @param GetSalableQuantityDataBySku $getSalableQuantityDataBySku
     * @param SellerProductsStock $sellerProductsStock
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly Elasticsearch $esAdapter,
        private readonly CollectionFactory $collectionFactory,
        private readonly GetSalableQuantityDataBySku $getSalableQuantityDataBySku,
        private readonly SellerProductsStock $sellerProductsStock,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param $ids
     * @return void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\InventoryConfigurationApi\Exception\SkuIsNotAssignedToStockException
     */
    public function executeFull($ids = null): void
    {
        $storeId = (int) $this->storeManager->getDefaultStoreView()->getId();
        $this->esAdapter->cleanIndex($storeId, self::INDEXER_NAME);

        $productCollection = $this->collectionFactory->create();
        $productCollection->setFlag('has_stock_status_filter', false);
        $productCollection->addAttributeToSelect('entity_id');
        $productCollection->addAttributeToSelect('sku');
        $productCollection->setStoreId(Store::DEFAULT_STORE_ID);
        $productCollection->getSelect()->columns(['entity_id']);
        if($ids && is_array($ids)){
            $productCollection->addFieldToFilter('entity_id', ['in' => $ids]);
        }
        if (!$productCollection->getSize()) {
            return;
        }
        $documents = [];
        foreach ($productCollection->getItems() as $product) {
            if($product->getSku()){
                try{
                    $salableQuantityData = $this->getSalableQuantityDataBySku->execute($product->getSku());
                } catch (\Exception $e){
                    $this->logger->error("No stock data for entity_id : ".$product->getEntityId()."\n Error:".$e->getMessage());
                    continue;
                }
                $salableQuantity = 0;
                foreach ($salableQuantityData as $stockItem) {
                    if($stockItem['qty']){
                        $salableQuantity = $salableQuantity + $stockItem['qty'];
                    }
                }
                $threshold = $this->sellerProductsStock->getSellerLowStockTreshold($product->getEntityId());
                $documents[$product->getId()] = [
                    'seller_low_stock_threshold_grid' => $this->sellerProductsStock->getStockStatus($salableQuantity, $threshold),
                    'entity_id' => $product->getEntityId(),
                ];
                $this->esAdapter->addDocs($documents, $storeId, self::INDEXER_NAME);
                $this->esAdapter->updateAlias($storeId, self::INDEXER_NAME);
            }
        }
    }

    /**
     * @param int[] $ids
     * @return void
     * @throws \Exception
     */
    public function execute($ids): void
    {
        $this->executeFull($ids);
    }

    /**
     * @param array $ids
     * @return void
     * @throws \Exception
     */
    public function executeList(array $ids): void
    {
        $this->execute($ids);
    }

    /**
     * @param $id
     * @return void
     * @throws \Exception
     */
    public function executeRow($id): void
    {
        $this->execute([$id]);
    }
}
