<?php
declare(strict_types=1);

namespace Comave\Marketplace\Ui\Component\Adminhtml\Listing\Column;

use Comave\Marketplace\Model\Service\SellerProductsStock;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Comave\Marketplace\Model\Service\GetProductQtyThresholdNotice;

class LowQtyThreshold extends Column
{
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param SellerProductsStock $helperData
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        private SellerProductsStock $sellerProductsStock,
        private GetProductQtyThresholdNotice $getProductQtyThresholdNotice,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source.
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            $fieldName = $this->getData('name');
            foreach ($dataSource['data']['items'] as &$item) {
                $indexStockMessage = $this->getProductQtyThresholdNotice->getProductQtyThresholdNotice((int)$item['entity_id']);
                if ($indexStockMessage) {
                    $item[$fieldName] = __($indexStockMessage);
                } else {
                    $sellerTreshold = $this->sellerProductsStock->getSellerLowStockTreshold($item['entity_id']);
                    $item[$fieldName] = $this->sellerProductsStock->getStockStatus((int)$item['qty'], $sellerTreshold);
                }
            }
        }
        return $dataSource;
    }
}
