<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Comave\Marketplace\Model\FixtureManager;
use Magento\Catalog\Model\Product;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class InstallProductMissingAttributes implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param FixtureManager $fixtureManager
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly FixtureManager $fixtureManager
    ) {
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function apply(): void
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $attributes = $this->fixtureManager->getData('product-missing-attributes');
        foreach ($attributes as $attributeData) {
            $code = $attributeData['attribute_code'];
            if (!$this->attributeExists($code)) {
                unset($attributeData['attribute_code']);
                $attributeData['group'] = 'General';
                $eavSetup->addAttribute(Product::ENTITY, $code, $attributeData);
            }
        }
    }

    /**
     * @param string $attributeCode
     * @return bool
     */
    private function attributeExists(string $attributeCode): bool
    {
        return (bool)$this->moduleDataSetup->getConnection()->fetchOne(
            $this->moduleDataSetup->getConnection()
                ->select()
                ->from('eav_attribute', 'attribute_code')
                ->where('attribute_code = ?', $attributeCode)
        );
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
