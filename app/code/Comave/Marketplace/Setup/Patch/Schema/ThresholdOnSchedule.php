<?php

declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Schema;

use Comave\Marketplace\Model\Indexer\ThresholdIndexer;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Indexer\Model\IndexerFactory;

class ThresholdOnSchedule implements DataPatchInterface
{
    /**
     * @param IndexerFactory $indexerFactory
     */
    public function __construct(private readonly IndexerFactory $indexerFactory)
    {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $indexer = $this->indexerFactory->create()->load(
            ThresholdIndexer::INDEXER_NAME
        );

        if ($indexer->getId()) {
            $indexer->setScheduled(true);
        }
    }

    /**
     * @return array|string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array|string[]
     */
    public function getAliases(): array
    {
        return [];
    }
}
