<?php
declare(strict_types=1);

namespace Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid;

use Comave\Marketplace\Model\Service\GetProductQtyThresholdNotice;
use Comave\Marketplace\Model\Service\SellerProductsStock;
use Magento\Backend\Block\Widget\Grid\Extended;
use Magento\InventorySalesAdminUi\Model\GetSalableQuantityDataBySku;

class Product extends Extended
{

    /**
     * @param \Magento\Backend\Block\Template\Context                    $context
     * @param \Magento\Backend\Helper\Data                               $backendHelper
     * @param \Magento\Framework\Registry                                $coreRegistry
     * @param \Magento\Catalog\Model\ProductFactory                      $productFactory
     * @param \Webkul\Marketplace\Model\ResourceModel\Product\Collection $sellerProduct
     * @param \Magento\Framework\Json\EncoderInterface                   $jsonEncoder
     * @param array                                                      $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        private \Magento\Framework\Registry $coreRegistry,
        private \Magento\Catalog\Model\ProductFactory $productFactory,
        private \Webkul\Marketplace\Model\ResourceModel\Product\Collection $sellerProduct,
        private \Magento\Framework\Json\EncoderInterface $jsonEncoder,
        private GetSalableQuantityDataBySku $getSalableQuantityDataBySku,
        private SellerProductsStock $sellerProductsStock,
        private GetProductQtyThresholdNotice $getProductQtyThresholdNotice,
        array $data = []
    ) {
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * Set defaults
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('seller_product_grid');
        $this->setDefaultSort('entity_at');
        $this->setUseAjax(true);
    }

    /**
     * Add column to collection
     *
     * @param Column $column
     * @return $this
     */
    protected function _addColumnFilterToCollection($column)
    {
        // Set custom filter for in adminassign flag
        if ($column->getId() == 'in_adminassign') {
            $productIds = $this->getSellerAssignedProducts();
            if (empty($productIds)) {
                $productIds = 0;
            }
            if ($column->getFilter()->getValue()) {
                $this->getCollection()->addFieldToFilter('entity_id', ['in' => $productIds]);
            } elseif (!empty($productIds)) {
                $this->getCollection()->addFieldToFilter('entity_id', ['nin' => $productIds]);
            }
        } else {
            parent::_addColumnFilterToCollection($column);
        }
        return $this;
    }

    /**
     * Apply various selection filters to prepare the sales order grid collection.
     *
     * @return $this
     */
    protected function _prepareCollection()
    {
        $this->setDefaultFilter(['in_adminassign' => 1]);
        $allOtherSellerProductIds = $this->getAllOtherSellerAssignedProducts();
        $collection = $this->productFactory->create()->getCollection()
        ->addAttributeToSelect(
            '*'
        );
        if (!empty($allOtherSellerProductIds)) {
            $collection->addFieldToFilter('entity_id', ['nin' => $allOtherSellerProductIds]);
        }

        $paramData = $this->getRequest()->getParams();
        if (!isset($paramData['filter'])) {
            $productIds = $this->getSellerAssignedProducts();
            if (empty($productIds)) {
                $productIds = 0;
            }
            $collection->addFieldToFilter('entity_id', ['in' => $productIds]);
        }

        if ($this->getLowStockFilter() !== null) {
            $productIds = $this->getSellerAssignedProducts();
            if (empty($productIds)) {
                $productIds = 0;
            }
            $collection->addFieldToFilter('entity_id', ['in' => $productIds]);
            $this->_addCustomFilterToCollection($collection);

        }

        $this->setCollection($collection);

        return parent::_prepareCollection();
    }

    /**
     * @return Extended
     * @throws \Exception
     */
    protected function _prepareColumns(): Extended
    {
        $this->addColumn(
            'in_adminassign',
            [
                'type' => 'checkbox',
                'name' => 'in_adminassign',
                'index' => 'entity_id',
                'data-form-part' => $this->getData('target_form'),
                'header_css_class' => 'col-select col-massaction',
                'column_css_class' => 'col-select col-massaction',
                'values' => $this->getSellerAssignedProducts()
            ]
        );
        $this->addColumn(
            'entity_id',
            [
                'header' => __('ID'),
                'index' => 'entity_id',
                'header_css_class' => 'col-id',
                'column_css_class' => 'col-id',
                'sortable' => true
            ]
        );
        $this->addColumn(
            'name',
            [
                'header' => __('Product Name'),
                'index' => 'name'
            ]
        );
        $this->addColumn(
            'sku',
            [
                'header' => __('Product SKU'),
                'index' => 'sku'
            ]
        );
        $this->addColumn(
            'price',
            [
                'header' => __('Product Price'),
                'index' => 'price',
                'type' => 'currency',
                'currency_code' => (string)$this->_scopeConfig->getValue(
                    \Magento\Directory\Model\Currency::XML_PATH_CURRENCY_BASE,
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                )
            ]
        );
        $this->addColumn(
            'low_stock_threshold',
            [
                'header' => __('Low Stock Threshold'),
                'index' => 'low_stock_threshold',
                'renderer' => \Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid\Column\Renderer\QtyThreshold::class,
                'filter_condition_callback' => array($this, '_preventDefaultFilter'),
                'sortable' => false
            ]
        );

        return parent::_prepareColumns();
    }

    /**
     * Get test_column filter from request using the same method in grid widget
     *
     * @return mixed|null
     */
    private function getLowStockFilter()
    {
        $filter = $this->getParam($this->getVarNameFilter(), null);

        if ($filter === null) {
            $filter = $this->_defaultFilter;
        }

        if (is_string($filter)) {
            $data = $this->_backendHelper->prepareFilterString($filter);
            $data = array_merge($data, (array)$this->getRequest()->getPost($this->getVarNameFilter()));
        } elseif ($filter && is_array($filter)) {
            $data = $filter;
        } elseif (0 !== sizeof($this->_defaultFilter)) {
            $data = $this->_defaultFilter;
        }

        if (array_key_exists('low_stock_threshold', $data)) {
            return $data['low_stock_threshold'];
        }
        return null;
    }

    /**
     * Add our custom filter based on renderer logic
     *
     * @param $collection
     * @return \Magento\Framework\Data\CollectionDataSourceInterface
     */
    private function _addCustomFilterToCollection($collection)
    {
        foreach ($collection->getItems() as $key => $row) {
            //renderer logic here
            $indexStockMessage = $this->getLowStockMessage($row);
            $filterValue = $this->getLowStockFilter();
            //remove item from collection if filter value is not equal to rendered value
            if ($filterValue != $indexStockMessage->getText()){
                $collection->addFieldToFilter(
                    "entity_id",
                    ['neq' => $row['entity_id']]
                );

            }
        }
        $collection->clear();
        return $collection;
    }

    private function getLowStockMessage($row){

        $indexStockMessage = $this->getProductQtyThresholdNotice->getProductQtyThresholdNotice((int)$row['entity_id']);
        if ($indexStockMessage) {
            return $indexStockMessage;
        } else {
            try{
                $salable = $this->getSalableQuantityDataBySku->execute($row->getSku());
            }catch (\Exception $e){
                $salable[0]['qty'] = 0;
            }
            $qty = 0;
            if (isset($salable[0]['qty'])) {
                $qty = (int)$salable[0]['qty'];
            }
            $sellerThreshold = $this->sellerProductsStock->getSellerLowStockTreshold($row['entity_id']);
            return $this->sellerProductsStock->getStockStatus($qty, $sellerThreshold);
        }
    }


    /**
     * Identify main table alias or its name if alias is not defined.
     *
     * @param $collection
     * @return string
     */
    private function getMainTableAlias($collection)
    {
        foreach ($collection->getSelect()->getPart(\Magento\Framework\DB\Select::FROM) as $tableAlias => $tableMetadata) {
            if ($tableMetadata['joinType'] == 'from') {
                return $tableAlias;
            }
        }
        return 'main_table';
    }


    /**
     * @param $collection
     * @param $column
     * @return $this
     */
    public function _preventDefaultFilter($collection, $column)
    {
        return $this;
    }

     /**
      * Retrieve the Url for a specified sales order row.
      *
      * @param \Magento\Sales\Model\Order|\Magento\Framework\DataObject $row
      * @return string
      */
    public function getRowUrl($row): string
    {
        return $this->getUrl('catalog/product/edit', ['id' => $row->getId()]);
    }

     /**
      * Get grid url
      *
      * @return void
      */
    public function getGridUrl()
    {
        return $this->getUrl('marketplace/seller/product', ['_current' => true]);
    }

    /**
     * Get seller assigned products
     *
     * @return array
     */
    protected function getSellerAssignedProducts(): array
    {
        $products = $this->sellerProduct->getAllAssignProducts(
            "`seller_id`=".(int)$this->getRequest()->getParam('id', 0)
        );
        return $products;
    }

    /**
     * Get all seller assigned products
     *
     * @return array
     */
    protected function getAllOtherSellerAssignedProducts(): array
    {
        $products = $this->sellerProduct->getAllAssignProducts(
            "`seller_id`!=".(int)$this->getRequest()->getParam('id', 0)
        );
        return $products;
    }

    /**
     * Get seller assigned products
     *
     * @return string
     */
    public function getSellerAssignedProductsJson(): string
    {
        $products = $this->getSellerAssignedProducts();
        if (!empty($products)) {
            return $this->jsonEncoder->encode($products);
        }
        return '{}';
    }
}
