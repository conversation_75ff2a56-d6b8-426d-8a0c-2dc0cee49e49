<?php
declare(strict_types=1);

namespace Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid\Column\Renderer;

use Comave\Marketplace\Model\Service\SellerProductsStock;
use Magento\Framework\DataObject;
use Magento\InventorySalesAdminUi\Model\GetSalableQuantityDataBySku;
use Comave\Marketplace\Model\Service\GetProductQtyThresholdNotice;

class QtyThreshold extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer
{
    /**
     * @param GetSalableQuantityDataBySku $getSalableQuantityDataBySku
     * @param SellerProductsStock $sellerProductsStock
     * @param GetProductQtyThresholdNotice $getProductQtyThresholdNotice
     */
    public function __construct(
        private GetSalableQuantityDataBySku $getSalableQuantityDataBySku,
        private SellerProductsStock $sellerProductsStock,
        private GetProductQtyThresholdNotice $getProductQtyThresholdNotice
    ) {
    }

    /**
     * @param DataObject $row
     * @return \Magento\Framework\Phrase|void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\InventoryConfigurationApi\Exception\SkuIsNotAssignedToStockException
     */
    public function render(DataObject $row): \Magento\Framework\Phrase
    {
        $indexStockMessage = $this->getProductQtyThresholdNotice->getProductQtyThresholdNotice((int)$row['entity_id']);
        if ($indexStockMessage) {
            return $indexStockMessage;
        } else {
            try{
                $salable = $this->getSalableQuantityDataBySku->execute($row->getSku());
            }catch (\Exception $e){
                $salable[0]['qty'] = 0;
            }
            $qty = 0;
            if (isset($salable[0]['qty'])) {
                $qty = (int)$salable[0]['qty'];
            }
            $sellerThreshold = $this->sellerProductsStock->getSellerLowStockTreshold($row['entity_id']);
            return $this->sellerProductsStock->getStockStatus($qty, $sellerThreshold);
        }
    }
}
