<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Override the default URL model to use frontend URLs in email context -->
    <preference for="Magento\Framework\UrlInterface" type="Comave\EmailConfig\Model\Url" />

    <!-- Register console command for testing -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave_email_config_test_urls" xsi:type="object">Comave\EmailConfig\Console\Command\TestEmailUrls</item>
            </argument>
        </arguments>
    </type>
</config>
