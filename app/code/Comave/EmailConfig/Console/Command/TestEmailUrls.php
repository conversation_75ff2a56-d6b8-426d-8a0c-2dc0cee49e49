<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Console command to test email URL generation
 */
class TestEmailUrls extends Command
{
    /**
     * @param UrlInterface $urlBuilder
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly UrlInterface $urlBuilder,
        private readonly StoreManagerInterface $storeManager
    ) {
        parent::__construct();
    }

    /**
     * Configure the command
     */
    protected function configure(): void
    {
        $this->setName('comave:test-email-urls')
            ->setDescription('Test email URL generation with frontend URLs');
    }

    /**
     * Execute the command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Testing email URL generation...</info>');

        // Test various customer-related URLs
        $testUrls = [
            'customer/account/',
            'customer/account/index/',
            'sales/order/history/',
            'sales/order/view/',
            'downloadable/download/link/'
        ];

        foreach ($testUrls as $routePath) {
            try {
                $url = $this->urlBuilder->getUrl($routePath, ['_nosid' => 1]);
                $output->writeln(sprintf(
                    '<comment>Route:</comment> %s <comment>=> URL:</comment> %s',
                    $routePath,
                    $url
                ));
            } catch (\Exception $e) {
                $output->writeln(sprintf(
                    '<error>Error generating URL for %s: %s</error>',
                    $routePath,
                    $e->getMessage()
                ));
            }
        }

        $output->writeln('<info>URL generation test completed.</info>');
        return Command::SUCCESS;
    }
}
