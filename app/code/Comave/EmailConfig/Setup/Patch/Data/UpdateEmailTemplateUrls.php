<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Psr\Log\LoggerInterface;

/**
 * Patch class to update email templates with frontend URLs instead of backend URLs.
 */
class UpdateEmailTemplateUrls implements DataPatchInterface
{
    /**
     * Email template codes that need URL updates
     */
    private const EMAIL_TEMPLATES_TO_UPDATE = [
        'sales_email_order_template',
        'sales_email_order_guest_template',
        'sales_email_invoice_template',
        'sales_email_invoice_guest_template',
        'sales_email_shipment_template',
        'sales_email_shipment_guest_template',
        'sales_email_creditmemo_template',
        'sales_email_creditmemo_guest_template',
        'new_order_weltpixel',
        'new_order_guest_weltpixel',
        'new_invoice_weltpixel',
        'new_invoice_guest_weltpixel',
        'new_shipment_weltpixel',
        'new_shipment_guest_weltpixel',
        'new_creditmemo_weltpixel',
        'new_creditmemo_guest_weltpixel'
    ];

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Apply the patch: update email templates to use frontend URLs
     *
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $this->updateEmailTemplateUrls();
        } catch (\Exception $e) {
            $this->logger->error('Failed to update email template URLs: ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Update email templates to use frontend URLs
     *
     * @return void
     */
    private function updateEmailTemplateUrls(): void
    {
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');

        // Get all email templates that might contain old URLs
        $select = $connection->select()
            ->from($tableName, ['template_id', 'template_text', 'orig_template_code'])
            ->where('template_text LIKE ?', '%customer/account%')
            ->orWhere('template_text LIKE ?', '%sales/order%');

        $templates = $connection->fetchAll($select);

        foreach ($templates as $template) {
            $updatedText = $this->updateTemplateText($template['template_text']);
            
            if ($updatedText !== $template['template_text']) {
                $connection->update(
                    $tableName,
                    ['template_text' => $updatedText],
                    ['template_id = ?' => $template['template_id']]
                );
                
                $this->logger->info(
                    'Updated email template URLs for template: ' . $template['orig_template_code']
                );
            }
        }
    }

    /**
     * Update template text to replace backend URLs with frontend URLs
     *
     * @param string $templateText
     * @return string
     */
    private function updateTemplateText(string $templateText): string
    {
        // Note: Since we've implemented the URL model preference, 
        // the templates should automatically use frontend URLs.
        // This patch is mainly for any hardcoded URLs that might exist.
        
        // Replace any hardcoded backend URLs with frontend URLs
        $replacements = [
            // Replace hardcoded backend URLs
            'mcstaging.comave.com' => '{{var frontend_base_url}}',
            'mcdev.comave.com' => '{{var frontend_base_url}}',
            
            // Ensure VIEW ORDER links use proper frontend paths
            'customer/account/' => 'account',
            'sales/order/history/' => 'account/orders',
            'sales/order/view/' => 'account/orders',
        ];

        $updatedText = $templateText;
        foreach ($replacements as $search => $replace) {
            $updatedText = str_replace($search, $replace, $updatedText);
        }

        return $updatedText;
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
