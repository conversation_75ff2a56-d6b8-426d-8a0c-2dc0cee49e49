<?php

declare(strict_types=1);

namespace Comave\BigBuyShipping\Model\RateProvider;

use Coditron\CustomShippingRate\Api\ShippingRateProviderInterface;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Coditron\CustomShippingRate\Model\ShipTableRatesFactory;
use Comave\BigBuy\Model\ConfigProvider;
use Comave\BigBuyShipping\Service\BigBuyShippingService;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class BigbuyRate implements ShippingRateProviderInterface
{
    /**
     * @param ConfigProvider $configProvider
     * @param ShipTableRatesFactory $rateFactory
     * @param LoggerInterface $logger
     * @param BigBuyShippingService $bigBuyShippingService
     * @param array $data
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly ShipTableRatesFactory $rateFactory,
        private readonly LoggerInterface $logger,
        private readonly BigBuyShippingService $bigBuyShippingService,
        private readonly array $data = []
    ) {
    }

    /**
     * @param RateRequest $request
     * @param string $sellerIdentifier
     * @param string $sellerId
     * @return array<ShipTableRates[]>
     */
    public function get(RateRequest $request, string $sellerIdentifier, string $sellerId): array
    {
        // If method is not active, or BigBuy integration not enabled, return false
        if (!$this->configProvider->isEnabled()) {
            $this->logger->notice(
                '[BigBuyShipping] Integration disabled'
            );

            return [];
        }

        // Early return if missing api key, redundant, also checked by ConfigurableApi further
        try {
            $apiKey = $this->configProvider->getApiKey();
        } catch (\Exception $e) {
            $this->logger->warning(
                '[BigBuyShipping] Integration error',
                [
                    'exception' => $e->getMessage()
                ]
            );

            return [];
        }
        if (!$apiKey) {
            $this->logger->warning(
                '[BigBuyShipping] Integration error - missing API Key',
            );

            return [];
        }

        try {
            $shippingData = $this->getShippingRates($request);
        } catch (ClientExceptionInterface|\Exception $e) {
            $this->logger->warning(
                '[BigBuyShipping] Integration error - request to fetch shipping rates failed',
                [
                    'message' => $e->getMessage(),
                ]
            );

            return [];
        }

        if (!$shippingData) {
            $this->logger->info(
                '[BigBuyShipping] Integration error - no shipping rates found',
            );

            return [];
        }

        /** @var ShipTableRates $rate */
        $rate = $this->rateFactory->create();
        /* @todo - move this to another logic for separation, but for MVP it's sufficient */
        usort($shippingData, function ($a, $b) {
            return $a['cost'] <=> $b['cost'];
        });

        $methodData = current($shippingData);
        /* @todo end */

        $methodName = $methodData['carrier_name'];

        if (!empty($methodData['delivery_time'])) {
            $methodName .= ' - ' . $methodData['delivery_time'];
            $rate->setTotalLeadTime(
                $this->leadTimeToInt($methodData['delivery_time'])
            );
        }

        $rate->setCourierName($methodName);

        if (isset($this->data['services'])) {
            $currentService = current($this->data['services']);
            $rate->setServiceType($currentService);
        }

        $rate->setShippingPrice((string) $methodData['cost']);

        return [$rate];
    }

    /**
     * Gets shipping rates from API for order shipping request
     * @param RateRequest $request
     * @return false|array
     * @throws ClientExceptionInterface|\Exception
     */
    private function getShippingRates(RateRequest $request): false|array
    {
        // build payload
        $products = $this->getProductsData($request);
        if (empty($products)) {
            return false;
        }

        return $this->bigBuyShippingService->fetchShippingRates(
            $products,
            $request->getDestCountryId(),
            $request->getDestPostcode(),
            true
        );
    }

    /**
     * Extract products data from Magento shipping rate request and return them as array structured for BigBuy payload
     * @param RateRequest $request
     * @return array
     */
    private function getProductsData(RateRequest $request): array
    {
        $products = [];

        foreach ($request->getAllItems() as $item) {
            // Skip virtual products (downloadable, services), Skip child items (simples, bundled)
            $product = $item->getProduct();
            if ($product->isVirtual() || $product->getParentItem()) {
                continue;
            }

            $products[] = [
                "reference" => $item->getSku(),
                "quantity" => (int) $item->getQty()
            ];
        }

        return $products;
    }

    /**
     * @param string $leadTime
     * @return int
     */
    private function leadTimeToInt(string $leadTime): int
    {
        $input = strtolower(trim($leadTime));
        $maxDays = 0;

        if (!preg_match('/(\d+)(?:\s*-\s*(\d+))?\s*(h|hour|hours|d|day|days|w|week|weeks)/', $input, $matches)) {
            return $maxDays;
        }

        $start = (int)$matches[1];
        $end = isset($matches[2]) ? (int)$matches[2] : $start;
        $unit = $matches[3];
        $max = max($start, $end);

        $conversionFactor = match ($unit) {
            'h', 'hour', 'hours' => 1 / 24,
            'd', 'day', 'days'   => 1,
            'w', 'week', 'weeks' => 7,
            default              => 0
        };

        return (int) ceil($max * $conversionFactor);
    }
}
