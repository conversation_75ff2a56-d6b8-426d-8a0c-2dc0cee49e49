<?php
declare(strict_types=1);

namespace Comave\LixGraphQl\Model\Resolver;

use Coditron\Lixtask\Api\CustomformRepositoryInterface;
use Coditron\Lixtask\Model\ImageUploader;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupFactory;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;

class LixResolver implements ResolverInterface
{
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly CustomformRepositoryInterface $lixTaskRepository,
        private readonly FilterBuilder $filterBuilder,
        private readonly FilterGroupFactory $filterGroupFactory,
        private readonly ImageUploader $imageUploader,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array
    {
        /** Temporary disabled by FE team
        $customerId = $context->getUserId();
        if (!$customerId) {
            throw new GraphQlAuthorizationException(
                __('The current customer isn\'t authorized.')
            );
        }
        */

        if ($args['status'] === false) {
            $status = \Coditron\Lixtask\Model\Enum\LixTaskStatus::STATUS_EXPIRED->value;
        } else {
            $status = \Coditron\Lixtask\Model\Enum\LixTaskStatus::STATUS_ACTIVE->value;
        }

        $filters = [
            $this->filterBuilder
                ->setField('status')
                ->setValue($status)
                ->setConditionType('eq')
                ->create()
        ];

        if (isset($args['taskId'])) {
            $filters[] = $this->filterBuilder
                ->setField('task_id')
                ->setValue($args['taskId'])
                ->setConditionType('eq')
                ->create();
        } else {
            $filters[] = $this->filterBuilder
                ->setField('task_id')
                ->setConditionType('neq')
                ->setValue('')
                ->create();
        }

        $filterGroups = [];
        foreach ($filters as $filter) {
            $filterGroup = $this->filterGroupFactory->create();
            $filterGroup->setFilters([$filter]);
            $filterGroups[] = $filterGroup;
        }

        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups($filterGroups)
            ->create();


        $lixTasks = $this->lixTaskRepository->getList($searchCriteria);

        $result = [];
        foreach ($lixTasks->getItems() as $lixTask) {
            $result[] = [
                'taskId' => $lixTask->getTaskId(),
                'titleShow' => $lixTask->getTitleShow(),
                'description' => $lixTask->getDescription(),
                'coinsPerAction' => (int)$lixTask->getCoinsPerAction(),
                'xpPoints' => (int)$lixTask->getXpPoints(),
                'rewardType' => $lixTask->getRewardType(),
                'proofType' => $lixTask->getProofType(),
                'status' => $lixTask->getStatus(),
                'customCurrencyOrganisationId' => $lixTask->getCustomCurrencyOrganisationId(),
                'isStaffApproved' => $lixTask->getIsStaffApproval(),
                'numberOfSubmissionsAllowed' => $lixTask->getNumberOfSubmissionsAllowed(),
                'periodAllowedFoSubmissions' => $lixTask->getPeriodAllowedForSubmissions(),
                'image' => $this->getLixFullBasePath($lixTask->getImage()),
                'rewardExpiresIn' => $lixTask->getRewardExpiresIn(),
                'taskButtonLabel' => $lixTask->getTaskButtonLabel(),
            ];
        }

        return $result;
    }

    private function getLixFullBasePath(?string $partialImagePath = ''): string
    {
        if (empty($partialImagePath)) {
            return '';
        }

        $lixTaskImage = $this->imageUploader->getBasePath() . $partialImagePath;
        try {
            $lixTaskImage = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA) . $lixTaskImage;
        } catch (\Exception $e) {
            return '';
        }

        return $lixTaskImage;
    }
}
