<?php
declare(strict_types=1);

namespace Comave\LixApiConnector\Helper;

use Coditron\Lixtask\Model\ResourceModel\Customform\CollectionFactory as CustomformCollectionFactory;
use Comave\Club\Model\Cookie\Custom;
use Exception;
use Guz<PERSON><PERSON>ttp\ClientFactory;
use Guz<PERSON>Http\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Directory\Model\Currency;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Store\Model\ScopeInterface;

/**
 * @deprecated : Please use the class Data from the module Comave\LixApiConnector\Mode\Config\ConfigProvider
 * Class Data
 * @package Comave\LixApiConnector\Helper
 */
class Data extends AbstractHelper
{
    /**
     * @var Json
     */
    private $json;
    /**
     * @var ClientFactory
     */
    private $clientFactory;
    const URL_PATH_KEY = 'sleekaccordian/general/odata_base_url';
    const URL_USER_KEY = 'sleekaccordian/general/user';
    const URL_ACCESS_KEY = 'sleekaccordian/general/access_key';
    const URL_SITE_KEY = 'sleekaccordian/general/site_url';
    const URL_HOST = 'sleekaccordian/general/host';
    const URL_PATH = 'sleekaccordian/general/path';

    const URL_ORGANISATION_KEY = 'sleekaccordian/general/organisation_id';
    const URL_PROJECT_KEY = 'sleekaccordian/general/project_id';
    const URL_CURRENCYORG_KEY = 'sleekaccordian/general/currency_organisation_id';
    const URL_TASKID_KEY = 'sleekaccordian/general/task_id';

    const URL_ITEM_REWARD = 'sleekaccordian/general/order_item_reward';
    const URL_ITEM_REWARD_SECOND = 'sleekaccordian/general/order_item_reward_second';
    const URL_REWARD_TYPE = 'sleekaccordian/general/order_reward_type';
    const URL_ITEMCOST_TYPE = 'sleekaccordian/general/order_item_cost';
    const URL_REWARDID = 'sleekaccordian/general/reward_id';
    const URL_REWARD_CURRENCY = 'sleekaccordian/general/reward_currency';
    const URL_LIXX_TASK_NAME = 'sleekaccordian/general/lixx_task_name';


    const URL_LIXPP_POINT = 'lix_general/cashpoint/cash_point';
    const URL_LIXPP_CURRENCY = 'lix_general/cashpoint/cash_currency';           // LIXCA(LIXPP) currency to fiat rate
    const URL_LIXX_CURRENCY = 'lix_general/lixxcurrencypoint/currency_lixx';    // LIXX currency to fiat rate
    const URL_LIXX_POINT = 'lix_general/lixxcurrencypoint/point_lixx';
    const URL_LIX_POINT = 'lix_general/lixcurrencypoint/point_lix';
    const URL_LIX_CURRENCY = 'lix_general/lixcurrencypoint/currency_lix';   // LIX currency to fiat rate
    const URL_LIXPP_WALLET = 'lix_general/cashpoint/lixpp_wallet_name';
    const URL_LIXX_WALLET = 'lix_general/lixxcurrencypoint/lixx_wallet_name';

    const URL_LIXPP_WALLET_ID = 'lix_general/cashpoint/lixpp_custom_currency_organisation_id';
    const URL_LIXX_WALLET_ID = 'lix_general/lixxcurrencypoint/lixx_custom_currency_organisation_id';

    const URL_LIXCA_CURRENCY_ID = 'lix_general/cashpoint/usd_id';
    const URL_LIX_CURRENCY_ID = 'lix_general/lixcurrencypoint/lix_id';  // warn: ID 2 is invalid
    const URL_LIXX_CURRENCY_ID = 'lix_general/lixxcurrencypoint/lixx_id';

    const URL_DEFAULT_OPTION = 'lix_general/general/select';

    const URL_FIRST_LIX_POINT = 'sleekaccordian/general/lix_task_id';
    const URL_EVERY_LIX_POINT = 'sleekaccordian/general/every_lix_task_id';
    const URL_FIRST_CASH_POINT = 'sleekaccordian/general/cashpoint_task_id';
    const URL_EVERY_CASH_POINT = 'sleekaccordian/general/every_cash_task_id';
    const URL_NEWS_POINT = 'sleekaccordian/general/news_id';
    const URL_NEWS_LIX_POINT = 'sleekaccordian/general/news_lix_id';

    const URL_LIXCA_REG_SCR_ID = 'sleekaccordian/general/reg_scratch_id_cash';
    const URL_LIXX_REG_SCR_ID = 'sleekaccordian/general/reg_scratch_id_lixx';
    const URL_LIXCA_REG_SCR_TITLE = 'sleekaccordian/general/reg_scratch_title_cash';
    const URL_LIXX_REG_SCR_TITLE = 'sleekaccordian/general/reg_scratch_title_lixx';

    const URL_LIXCA_TREASURE_HUNT_ID = 'sleekaccordian/general/treasure_hunt_id_cash';
    const URL_LIXX_TREASURE_HUNT_ID = 'sleekaccordian/general/treasure_hunt_id_lixx';

    const IS_ENABLE = 'customer_seller_general/general/extension_enabled';
    const SELLER_ATTRIBUTE_SELECTED = 'customer_seller_general/seller_profileness/seller_attributes';
    const CUSTOMER_ATTRIBUTE_SELECTED = 'customer_seller_general/customer_profileness/customer_attributes';

    /** @var array|string[] */
    public const array CRYPTO_CURRENCIES_ID_CONFIGS = [
        'LIXCA(LIXPP)' => self::URL_LIXCA_CURRENCY_ID,
        'LIX' => self::URL_LIX_CURRENCY_ID,
        'LIXX' => self::URL_LIXX_CURRENCY_ID,
    ];

    private Custom $_myCookie;
    private CustomerRepositoryInterface $customerRepository;
    private CustomerFactory $customerFactory;
    private CustomformCollectionFactory $customformCollectionFactory;
    private ManagerInterface $messageManager;

    public function __construct(
        Custom $myCookie,
        Context $context,
        CustomerRepositoryInterface $customerRepository,
        CustomerFactory $customerFactory,
        Json $json,
        ClientFactory $clientFactory,
        ManagerInterface $messageManager,
        CustomformCollectionFactory $customformCollectionFactory
    ) {
        parent::__construct($context);
        $this->_myCookie = $myCookie;
        $this->customerRepository = $customerRepository;
        $this->customerFactory = $customerFactory;
        $this->json = $json;
        $this->clientFactory = $clientFactory;
        $this->messageManager = $messageManager;
        $this->customformCollectionFactory = $customformCollectionFactory;
    }

    public function getConfigValue($field, $storeId = null)
    {
        return $this->scopeConfig->getValue(
            $field,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getLixcaCurrencyId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXCA_CURRENCY_ID, $storeId);
    }

    public function getLixCurrencyId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIX_CURRENCY_ID, $storeId);
    }

    public function getLixxCurrencyId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_CURRENCY_ID, $storeId);
    }

    public function getSiteUrl($storeId = null)
    {
        return $this->getConfigValue(self::URL_SITE_KEY, $storeId);
    }

    public function getHost($storeId = null)
    {
        return $this->getConfigValue(self::URL_HOST, $storeId);
    }

    public function getPath($storeId = null)
    {
        return $this->getConfigValue(self::URL_PATH, $storeId);
    }

    public function getToken($storeId = null)
    {
        return $this->getConfigValue(self::URL_ACCESS_KEY, $storeId);
    }

    public function getOrganisationId($storeId = null)
    {
        return $this->getConfigValue(self::URL_ORGANISATION_KEY, $storeId);
    }

    public function getProjectId($storeId = null)
    {
        return $this->getConfigValue(self::URL_PROJECT_KEY, $storeId);
    }

    public function getCurrencyOrgId($storeId = null)
    {
        return $this->getConfigValue(self::URL_CURRENCYORG_KEY, $storeId);
    }

    public function getLixcaWallet($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_WALLET, $storeId);
    }

    public function getLixxWallet($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_WALLET, $storeId);
    }

    public function getLixxWalletId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_WALLET_ID, $storeId);
    }

    public function getLicaWalletId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_WALLET_ID, $storeId);
    }

    // start
    public function getLixWalletId($lixwalletid, $storeId = null)
    {
        if ($lixwalletid == "LIXX") {
            return $this->getConfigValue(self::URL_LIXX_WALLET_ID, $storeId);
        } else {
            return $this->getConfigValue(self::URL_LIXPP_WALLET_ID, $storeId);
        }
    }

    public function getTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_TASKID_KEY, $storeId);
    }

    public function getItemReward($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEM_REWARD, $storeId);
    }

    public function getRewardType($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARD_TYPE, $storeId);
    }

    public function getRewardId($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARDID, $storeId);
    }

    public function getRewardCurrency($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARD_CURRENCY, $storeId);
    }

    public function getLixxTaskName($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_TASK_NAME, $storeId);
    }

    public function getItemCost($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEMCOST_TYPE, $storeId);
    }

    public function getItemRewardSecond($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEM_REWARD_SECOND, $storeId);
    }

    public function getCashPointValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_CASH_POINT, $storeId);
    }

    public function getCashCurrenciesValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_CURRENCY, $storeId);
    }

    public function getLixxCurrenciesValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_CURRENCY, $storeId);
    }

    public function getLixPointValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIX_POINT, $storeId);
    }

    public function getLixCurrenciesValues($cemail, $attributeCode, $storeId = null)
    {
        $customer = $this->customerFactory->create();
        $customer->setWebsiteId(1);
        $customer->loadByEmail($cemail);
        $testAttr = $customer->getData($attributeCode);

        if ($testAttr == 'LIXX') {
            return $this->getConfigValue(self::URL_LIXX_CURRENCY);
        } else {
            return $this->getConfigValue(self::URL_LIXPP_CURRENCY);
        }
    }

    public function getDefaultPointOption($storeId = null)
    {
        return $this->scopeConfig->getValue(
            'lix_general/general/select',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    public function getFirstLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_FIRST_LIX_POINT, $storeId);
    }

    public function getEveryLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_EVERY_LIX_POINT, $storeId);
    }

    public function getFirstCashTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_FIRST_CASH_POINT, $storeId);
    }

    public function getEveryCashTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_EVERY_CASH_POINT, $storeId);
    }

    public function getNewsLetterTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_NEWS_POINT, $storeId);
    }

    public function getNewsLetterLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_NEWS_LIX_POINT, $storeId);
    }

    public function getRegScrtchCashId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXCA_REG_SCR_ID, $storeId);
    }

    public function getRegScrtchLixxId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_REG_SCR_ID, $storeId);
    }

    public function getRegScrtchCashTitle($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXCA_REG_SCR_TITLE, $storeId);
    }

    public function getRegScrtchLixxTitle($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_REG_SCR_TITLE, $storeId);
    }

    public function getTreasureHuntCashId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXCA_TREASURE_HUNT_ID, $storeId);
    }

    public function getTreasureHuntLixxId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_TREASURE_HUNT_ID, $storeId);
    }

    public function getDefaultStoreCurrencyCode($storeId = null)
    {
        return $this->getConfigValue(Currency::XML_PATH_CURRENCY_DEFAULT, $storeId);
    }

    private function makeRequest($url, $method, $developerToken, $bearer_token, $data = null)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if ($data !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'Developer-Token: '.$developerToken,
                'Authorization: Bearer '.$bearer_token,
                'x-api-base: '.$developerToken,
            ]);
            $response = json_decode(curl_exec($ch), true);

            return $response;
        } catch (Exception $e) {
            return false;
        }
    }

    private function doRequest(
        string $uriEndpoint,
        array $params = [],
        array $headers = [],
        string $requestMethod = Request::HTTP_METHOD_GET
    ): Response {
        // Merge default headers with provided headers
        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json; charset=utf-8',
        ];
        $headers = array_merge($defaultHeaders, $headers);
        $endpoint = $this->getSiteUrl();
        $client = $this->clientFactory->create([
            'config' => [
                'base_uri' => $endpoint,
            ],
        ]);

        try {
            $response = $client->request($requestMethod, $uriEndpoint, [
                'headers' => $headers,
                'json' => $params, // Assuming params are JSON serializable
            ]);
        } catch (GuzzleException $exception) {
            // Handle exceptions if needed
            $statusCode = $exception->getCode();
            $reason = $exception->getMessage();
            $response = new Response($statusCode, [], $reason);
        }

        return $response;
    }

    /**
     * Refactor and verify Place Order performance after
     * @param $custData
     * @return mixed
     */
    public function setLixCustomers($custData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url."user/register");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $custData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /**
     *  TODO: Refactor and verify Place Order Performance after
     * @param $lemail
     * @return mixed
     */
    public function getLixUserEmail($lemail)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token: ".$access_token;

        $postfields = array();
        $postfields['email'] = $lemail;
        $postData = json_encode($postfields);

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url."user/email/verify");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                [
                    'Accept: application/json',
                    'Content-Type: application/json; charset=utf-8',
//                    'x-api-base: '. $developerToken,      // when this is present on this url, it returns 'Server error'
                    $developerToken,
                ]
            );
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception('Curl error: '.curl_error($ch));
            }

            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode >= 400) {
                throw new Exception('HTTP error: '.$httpCode);
            }

            if ($httpCode >= 300) {
                throw new Exception('HTTP redirect: '.$httpCode);
            }

            $decodedResponse = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON decode error: '.json_last_error_msg());
            }
        } catch (Exception $e) {
            $this->_logger->error('Exception in setUserToken: '.$e->getMessage());
            $this->messageManager->addErrorMessage($e->getMessage());

            return null;
        }

        return $decodedResponse;
    }

    /**
     *  TODO: Replace this wherever is used using the new LixApi module.
     *
     * @param $lixid
     * @return false
     * @deprecated
     */
    public function getLixOrganisationList($lixid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $organisation_id = $this->getOrganisationId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."organisations/".$lixid."/show");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    /**
     *  TODO: Remove this method. Not used anymore.
     *
     * @param $lproid
     * @return false
     * @deprecated
     */
    public function getLixProjectList($lproid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $project_id = $this->getProjectId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."projects/".$lproid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    /**
     * TODO: Refactor and verify Place Order Performance after
     *
     * @param $lproid
     * @param $nxtUrl
     * @return array|false
     */
    public function getAllLixTaskList($lproid, $nxtUrl)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $finalArray = array();
        $project_id = $lproid;
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $nxtUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
            $nextUrl = $response['data']['links']['next'];

            foreach ($response['data']['data'] as $value) {
                $taskData = array();
                $taskData['id'] = $value['id'];
                $taskData['title'] = $value['title'];
                $finalArray[] = $taskData;
            }
            if ($nextUrl != "null") {
                $this->getAllLixTaskList($project_id, $nextUrl);
            }

        } catch (Exception $e) {
            return false;
        }

        return $finalArray;
    }

    public function getLixTaskList($ltaskid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/".$ltaskid."/show");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getLixReward($ltaskid, $rewardData, $rewardValue)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();
        $itemReward = $this->getItemReward();
        $rewardId = $this->getRewardId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."task/".$ltaskid."/reward/configuration");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $rewardData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getLixActivity($ltaskid, $activityData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        //$task_id = $this->getTaskId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/activity/".$ltaskid."/submit");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $activityData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function getLixBalanceByEmail($email, $rew_curr)
    {

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        $postfields['email'] = $email;
        $postfields['wallet'] = $rew_curr;
        $postRewardfields = json_encode($postfields);

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."get/wallet/balance");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postRewardfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function updateLixBalance($email, $lixPoints, $rew_curr)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        $postfields['email'] = $email;
        $postfields['wallet'] = $rew_curr;
        $postfields['amount'] = $lixPoints;
        $postRewardfields = json_encode($postfields);

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."wallet/update/balance");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postRewardfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getLixEmailExist($orgId, $lemail)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $orgId = $this->getOrganisationId();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url."organisations/members/search/".$orgId."/".$lemail."");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $response;
    }

    /**
     * @param $currency_id string Also named Cashpoint ID
     * @param $currency_fiat string Symbol of fiat that is supported
     * @return float
     * {
     *      "success": true,
     *      "data": {
     *          "crypto": "LIXX",
     *          "fiat": "USD",
     *          "rate": 0.001118,
     *          "conversionrate": 0.001117664760503983,
     *          "example": "1 LIXX === 0.001118 USD"
     *      },
     *      "message": "Conversion rate fetched successfully"
     *  }
     * Observed:
     * - in case currency id is not supported, it responds with 200 OK but no response body
     * - in case currency fiat is not supported, it responds with 500 error
     * - rate == conversionrate only for USD fiat, otherwise conversionrate differs!
     * @throws \Exception
     */
    public function getCashPointRate($currency_id, $currency_fiat): float
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $ch = curl_init();
        curl_setopt(
            $ch,
            CURLOPT_URL,
            $url."cash-point/rate?currency_id=".$currency_id."&currency_fiat=".$currency_fiat
        );
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_POST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'x-api-base: '.$developerToken,
            $developerToken,
            $authorization,
        ]);
        $response = json_decode(curl_exec($ch), true);

        if (isset($response['error'])) {
            throw new Exception('Error: '.$response['error']);
        }

        if (!$response
            || !isset($response['data'])
            || !isset($response['data']['rate'])
            || !isset($response['success'])
            || !$response['success']
        ) {
            throw new Exception('Invalid rate response');
        }

        return (float)$response['data']['rate'];
    }

    /**
     * @param null $storeId
     * @return float
     * @throws \Exception
     */
    public function getLixcaRate($storeId = null): float
    {
        return $this->getCashPointRate(
            $this->getLixcaCurrencyId($storeId),
            $this->getDefaultStoreCurrencyCode($storeId)
        );
    }

    /**
     * @warning Seems id $currency_id = 2 does not exist on api, response has empty body
     * @param null $storeId
     * @return float
     * @throws \Exception
     */
    public function getLixRate($storeId = null): float
    {
        return $this->getCashPointRate(
            $this->getLixCurrencyId($storeId = null),
            $this->getDefaultStoreCurrencyCode($storeId)
        );
    }

    /**
     * @param null $storeId
     * @return float
     * @throws \Exception
     */
    public function getLixxRate($storeId = null): float
    {
        return $this->getCashPointRate(
            $this->getLixxCurrencyId($storeId),
            $this->getDefaultStoreCurrencyCode($storeId)
        );
    }

    /**
     * @param $orgId
     * @return array
     */
    public function getClubList($orgId): array
    {
        $dataResponse = [];
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $orgId = $this->getOrganisationId();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url."organisations/get/clublists/".$orgId);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $result = curl_exec($ch);
            if ($result) {
                if (!empty($response) && array_key_exists("data", $response)) {
                    if ($response['success']) {
                        $clubArray = $response['data']['data'];

                        $dataResponse['success'] = $response['success'];
                        $dataResponse['message'] = $response['message'];
                        if ($clubArray) {
                            foreach ($clubArray as $k => $clubdata) {
                                $dataResponse ['data'][] = [
                                    'clubid' => $clubdata['id'],
                                    'clubuid' => $clubdata['external_unique_identifier'],
                                    'clubname' => $clubdata['name'],
                                ];

                            }
                        }
                    } else {
                        $dataResponse['success'] = $response['success'];
                        $dataResponse['message'] = $response['message'];
                    }
                } else {
                    $dataResponse['success'] = $response['success'];
                    $dataResponse['message'] = $response['message'];
                }
            }
        } catch (Exception $e) {
            return [];
        }

        return $dataResponse;
    }


    public function getQrCode($email, $currency_id, $amount, $currency_fiat)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();


        try {
            $ch = curl_init();
            curl_setopt(
                $ch,
                CURLOPT_URL,
                $url."payment/lixpay/transaction-qr?user_id=".$email."&currency_id=".$currency_id."&amount=".$amount."&currency_fiat=".$currency_fiat
            );
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }


    public function setCookieValue($value, $duration)
    {
        return $this->_myCookie->set($value, $duration);
    }

    public function getCookieValue()
    {
        return $this->_myCookie->get();
    }

    public function getCustLixWallet($customerId, $attributeCode)
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            if ($customer->getCustomAttribute($attributeCode)) {
                $customAttribute = $customer->getCustomAttribute($attributeCode);
            }

            return $customAttribute->getValue();
        } catch (Exception $e) {
            return null;
        }
    }

    public function getWalletList($customerId)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        $entity = "user";

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."get-wallet-details/".$entity."/".$customerId);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getBalance($email, $lixwallet)
    {
        $postfields = array();
        $postfields['email'] = $email;
        $postfields['wallet'] = $lixwallet;
        $postBalancefields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."get/wallet/balance");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postBalancefields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getLixpayRate($currency_id, $currency_fiat)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        try {
            $ch = curl_init();
            curl_setopt(
                $ch,
                CURLOPT_URL,
                $url."payment/lixpay/rate?currency_id=".$currency_id."&currency_fiat=".$currency_fiat
            );
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function getOtp($user_id)
    {
        $postfields = array();
        $postfields['user_id'] = $user_id;
        $postUserIdfields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/pin/verify/otp");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function verifyOtp($user_id, $pin, $otp)
    {
        $postfields = array();
        $postfields['user_id'] = $user_id;
        $postfields['pin'] = $pin;
        $postfields['otpcode'] = $otp;
        $postUserIdfields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/update-pin");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getWalletListDetails($uuid)
    {
        $postfields = array();
        $postfields['organisation_id'] = $this->getOrganisationId();
        $postfields['Developer-Token'] = $this->getToken();
        $postUserIdfields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/scan/".$uuid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getWalletListurl($uuid)
    {
        $organisationId = $this->getOrganisationId();
        $url = $this->getSiteUrl();
        $developerToken = $this->getToken();
        $walletDetailsurl = $url."payment/lixpay/scan/".$uuid."?Developer-Token=".$developerToken."&organisation_id=".$organisationId;

        return $walletDetailsurl;
    }

    public function getWalletDetails($user_id)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/wallets?user_id=".$user_id);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getMarketOffers($page, $keyword, $category, $country, $orderby, $currency_ids)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt(
                $ch,
                CURLOPT_URL,
                $url."lix/global/brands?country=".$country."&keyword=".$keyword."&category=".$category."&orderBy=".$orderby."&custom_currency=".$currency_ids."&page=".$page
            );
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getMarkets($page = null)
    {
        $url = $this->getSiteUrl()."markets";
        if ($page) {
            $url = $url."?page=".$page;
        }

        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getCategoryList()
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."marketplace/categories");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getCountryList()
    {
        // @TODO: refactor to a single method service
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."countries");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getMarketOffersbyId($id)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."lix/global/show/detail/".$id);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getRate($currencyId, $currencyFiat)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt(
                $ch,
                CURLOPT_URL,
                $url."cash-point/rate?currency_id=".$currencyId."&currency_fiat=".$currencyFiat
            );
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function buyCoupon($item_id, $brand, $currency, $amount, $custom_currency_id, $redeemer_email)
    {
        $postfields = array();
        $postfields['item_id'] = $item_id;
        $postfields['brand'] = $brand;
        $postfields['currency'] = $currency;
        $postfields['amount'] = $amount;
        $postfields['custom_currency_id'] = $custom_currency_id;
        $postfields['redeemer_email'] = $redeemer_email;
        $organisation_id = $this->getOrganisationId();
        $postfields['organisation_id'] = $organisation_id;
        $postUserIdfields = json_encode($postfields);

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."lix/global/checkout");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getCouponlist($bearer_token)
    {
        $url = $this->getSiteUrl()."get/user/coupons";

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function getCouponDetail($coupon_id, $bearer_token)
    {
        $url = $this->getSiteUrl()."get/coupon/detail/".$coupon_id;

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function getApproveTran($uuid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/approve-transaction/".$uuid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function getCancelTran($uuid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/cancel-transaction/".$uuid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;

    }


    public function getPendingTran($user_id)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/pending/transaction/".$user_id);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function setUserToken($email, $password, $device, $os)
    {
        $postfields = [
            'email' => $email,
            'password' => $password,
            'device_model' => $device,
            'os_type' => $os,
        ];
        $postUserIdfields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."user/login");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);

            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception('Curl error: '.curl_error($ch));
            }

            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode >= 400) {
                throw new Exception('HTTP error: '.$httpCode);
            }

            if ($httpCode >= 300) {
                throw new Exception('HTTP redirect: '.$httpCode);
            }

            $decodedResponse = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON decode error: '.json_last_error_msg());
            }
        } catch (Exception $e) {
            $this->_logger->error('Exception in setUserToken: '.$e->getMessage());

            return false;
        }

        return $decodedResponse;
    }

    public function getTransactionStatus($uuid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        $postfields = array();
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/transaction-status/".$uuid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function tranByPin($user_id, $uuid, $pin)
    {
        $postfields = array();
        $postfields['user_id'] = $user_id;
        $postfields['uuid'] = $uuid;
        $postfields['pin'] = $pin;

        $postUserIdfields = json_encode($postfields);
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."payment/lixpay/pin-authorization");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function setLixTask($activityData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/create_new");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $activityData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    // @TODO: refactor select only the important attributes using query
    public function getSellerAttributesData($customerId)
    {
        try {
            $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
            $customer = $objectManager->create('Magento\Customer\Model\Customer')->load($customerId);
            $selectedAttributes = $this->getSellerSelectedAttributes();
            $customerData = [];

            foreach ($selectedAttributes as $attributeCode) {
                $customerData[$attributeCode] = $customer->getData($attributeCode);
            }

            return $customerData;
        } catch (Exception $e) {
            return [];
        }
    }

    protected function getCustomerSelectedAttributes()
    {
        $selectedAttributes = $this->scopeConfig->getValue(
            self::CUSTOMER_ATTRIBUTE_SELECTED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        if ($selectedAttributes !== null && $selectedAttributes !== '') {
            return explode(',', $selectedAttributes);
        } else {
            return [];
        }
    }

    public function getProfileCompletionPercentageSeller($customerId): int
    {
        $customerData = $this->getSellerAttributesData($customerId);
        $totalAttributes = count($this->getSellerSelectedAttributes());
        $filledAttributes = count(array_filter($customerData));
        if ($totalAttributes == 0) {
            return 0;
        }
        $profileCompletionPercentage = ($filledAttributes / $totalAttributes) * 100;

        return (int)$profileCompletionPercentage;
    }

    protected function getSellerSelectedAttributes(): array
    {
        $selectedAttributes = $this->scopeConfig->getValue(
            self::SELLER_ATTRIBUTE_SELECTED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        if ($selectedAttributes !== null && $selectedAttributes !== '') {
            return explode(',', $selectedAttributes);
        } else {
            return [];
        }
    }

    public function getCharitylist()
    {
        $url = $this->getSiteUrl()."charity-organisations";
        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function donatePoint($wallet_id, $charity_id, $amount, $lixtoken)
    {
        $url = $this->getSiteUrl()."giving-wallets/give";
        $bearer_token = $lixtoken;
        $postfields = array();
        $postfields['charity_foundation_organisation_id'] = $charity_id;
        $postfields['custom_currency_id'] = $wallet_id;
        $postfields['charity_amount'] = $amount;

        return $this->makeRequest($url, 'POST', $bearer_token, $bearer_token, $postfields);
    }

    public function getRecommOffersList($currency_ids)
    {
        $url = $this->getSiteUrl()."markets/recommended?custom_currency=".$currency_ids;
        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function updateLixTask($taskid, $activityData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/".$taskid."/update");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $activityData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getEarnbyBrand($unique_identifier)
    {
        $organisation_id = $this->getOrganisationId();
        $url = $this->getSiteUrl(
            )."analytics/earnings-by-brand/".$organisation_id."?externalUniqueIdentifier=".$unique_identifier;
        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function getSpendbyBrand($unique_identifier)
    {
        $organisation_id = $this->getOrganisationId();
        $url = $this->getSiteUrl(
            )."analytics/spending-by-brand/".$organisation_id."?externalUniqueIdentifier=".$unique_identifier;
        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function verifyScratchLink($apiTaskLink, $lixtoken)
    {
        $postfields = array();
        $postfields['link'] = $apiTaskLink;
        $postUserIdfields = json_encode($postfields);

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $bearer_token = $lixtoken;

        $developerToken = "Developer-Token:".$bearer_token;
        $authorization = "Authorization: Bearer ".$bearer_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."qrcode/link/verify");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getRewardoption($apiTaskLink)
    {
        $postfields = array();
        $postfields['link'] = $apiTaskLink;
        $postUserIdfields = json_encode($postfields);

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $developerToken = "Developer-Token:".$access_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."qrcode/link/verify");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function submitScratchActivity($lixtoken, $ltaskid, $activityData)
    {

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $bearer_token = $lixtoken;

        $developerToken = "Developer-Token:".$bearer_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/activity/".$ltaskid."/submit");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $activityData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);

            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;

    }

    public function submitspinwheeltask($taskId, $lixtoken, $email, $phone)
    {
        $postfields = array();

        $postfields['phone'] = $phone;
        $postfields['email'] = $email;
        $postUserIdfields = json_encode($postfields);

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();
        $bearer_token = $lixtoken;

        $developerToken = "Developer-Token:".$bearer_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/activity/".$taskId."/submit");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postUserIdfields);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function submitquiz($taskId, $lixtoken, $jquizdata)
    {

        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $bearer_token = $lixtoken;

        $developerToken = "Developer-Token:".$bearer_token;
        $authorization = "Authorization: Bearer ".$access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."tasks/activity/".$taskId."/submit");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jquizdata);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (Exception $e) {
            return false;
        }

        return $response;
    }

    public function getTodaysMatch()
    {
        $organisation_id = $this->getOrganisationId();
        $url = $this->getSiteUrl()."predictor-task/fixtures/{league_id}?league_id=9";
        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function submitUserPrediction($lixtoken, $activityData)
    {
        $url = $this->getSiteUrl();
        $developerToken = "Developer-Token:".$lixtoken;
        $authorization = "Authorization: Bearer ".$lixtoken;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."predictor-task/submit-user-prediction");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $activityData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                'x-api-base: '.$developerToken,
                $developerToken,
                $authorization,
            ]);

            $response = json_decode(curl_exec($ch), true);


        } catch (Exception $e) {
            return false;
        }

        return $response;
    }


    public function getPredictListMatch($lixwallet)
    {

        $organisation_id = $this->getOrganisationId();
        $currency_org_id = $lixwallet;

        $url = $this->getSiteUrl(
            )."predictor-task/prediction-tasks?organisation_id={$organisation_id}&custom_currency_org_id={$currency_org_id}";

        $bearer_token = $this->getToken();

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function getPredictListUser($lixtoken, $lixwallet)
    {
        $organisation_id = $this->getOrganisationId();
        $currency_org_id = $lixwallet;

        $url = $this->getSiteUrl(
            )."predictor-task/user-predictions?organisation_id={$organisation_id}&custom_currency_org_id={$currency_org_id}";


        return $this->makeRequest($url, 'GET', $lixtoken, $lixtoken, null);
    }

    public function getSpinWheelList($status, $lixtoken)
    {
        $orgid = $this->getOrganisationId();
        $url = $this->getSiteUrl()."tasks/spin-the-wheel-tasks/{$orgid}?organisation_id={$orgid}";
        if ($status) {
            $url .= "&status={$status}";
        }
        $bearer_token = $this->getToken();
        $postfields = array();
        $postfields['organisation_id'] = $orgid;

        return $this->makeRequest($url, 'GET', $bearer_token, $lixtoken, $postfields);
    }

    public function getscratchCardsList($status, $lixtoken)
    {
        $orgid = $this->getOrganisationId();
        $url = $this->getSiteUrl()."tasks/scratch-card-tasks/{$orgid}?organisation_id={$orgid}";
        if ($status) {
            $url .= "&status={$status}";
        }
        $bearer_token = $this->getToken();

        $postfields = array();
        $postfields['organisation_id'] = $orgid;

        return $this->makeRequest($url, 'GET', $bearer_token, $lixtoken, $postfields);

    }


    public function getQuizList($status, $lixtoken)
    {
        $orgid = $this->getOrganisationId();

        $url = $this->getSiteUrl()."tasks/quiz-tasks/{$orgid}?organisation_id={$orgid}";
        if ($status) {
            $url .= "&status={$status}";
        }
        $bearer_token = $this->getToken();

        $postfields = array();
        $postfields['organisation_id'] = $orgid;

        return $this->makeRequest($url, 'GET', $bearer_token, $lixtoken, $postfields);

    }

    public function getpreditTaskList($status = null)
    {

        $orgid = $this->getOrganisationId();
        $url = $this->getSiteUrl()."tasks/predit-tasks/{$orgid}?organisation_id={$orgid}";
        if ($status) {
            $url .= "&status={$status}";
        }
        $bearer_token = $this->getToken();

        $postfields = array();
        $postfields['organisation_id'] = $orgid;

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, $postfields);

    }

    public function getLixOffers()
    {
        $orgid = $this->getOrganisationId();
        $bearer_token = $this->getToken();
        $url = $this->getSiteUrl()."organisations/".$orgid."/offers?status=active";

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function getLixOffersbyId($id)
    {
        $bearer_token = $this->getToken();
        $url = $this->getSiteUrl()."markets/".$id."?offer_type=market";

        return $this->makeRequest($url, 'GET', $bearer_token, $bearer_token, null);
    }

    public function buyLixCoupon($item_id, $custom_currency_id, $redeemer_email)
    {
        $bearer_token = $this->getToken();
        $url = $this->getSiteUrl()."markets/buy_offer";
        $postfields = array();
        $postfields['offer'] = $item_id;
        $postfields['currency_id'] = $custom_currency_id;
        $postfields['redeemer_email'] = $redeemer_email;

        return $this->makeRequest($url, 'POST', $bearer_token, $bearer_token, $postfields);
    }

    public function submitTreasuerGame($email, $taskId)
    {
        $bearer_token = $this->getToken();
        $url = $this->getSiteUrl()."tasks/activity/".$taskId."/submit";
        $postfields = array();
        $postfields['email'] = $email;

        return $this->makeRequest($url, 'POST', $bearer_token, $bearer_token, $postfields);
    }

}
