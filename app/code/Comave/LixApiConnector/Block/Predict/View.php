<?php
namespace Comave\LixApiConnector\Block\Predict;

use Comave\LixApiConnector\Model\Service\UserLixToken;
use Magento\Framework\View\Element\Template;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\Request\Http;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Message\ManagerInterface;
use Coditron\NotificationLogs\Model\ResourceModel\AppNotification\CollectionFactory;

class View extends Template
{
    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\View\Element\Template\Context $context,
        UrlInterface $url,
        Http $request,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        ResourceConnection $resourceConnection,
        \Comave\LixApiConnector\Helper\Data $apiHelper,
        \Magento\Framework\Session\SessionManagerInterface $coreSession,
        ResultFactory $resultFactory,
        JsonFactory $resultJsonFactory,
        PageFactory $resultPageFactory,
        \Coditron\Lixtask\Helper\Api $dataHelper,
        ManagerInterface $messageManager,
        CollectionFactory $collectionFactory,
        array $data = []
    ) {
        $this->_storeManager = $storeManager;
        $this->_url = $url;
        $this->request = $request;
        $this->_customerSession = $customerSession;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_resourceConnection = $resourceConnection;
        $this->apiHelper = $apiHelper;
        $this->resultFactory = $resultFactory;
        $this->coreSession = $coreSession;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->_resultPageFactory = $resultPageFactory;
        $this->dataHelper = $dataHelper;
        $this->messageManager = $messageManager;
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context, $data);
    }
    protected function _prepareLayout(){
        return parent::_prepareLayout();
    }

    public function getUserToken(){
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/Tokenpredict.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In Here");

        $lixtoken = '';
        $customerId = $this->_customerSession->getCustomer()->getId();
        $logger->info(print_r($customerId, true) . ' customerId');
        $customeratt = $this->_customerRepositoryInterface->getById($customerId);
        if($customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)) {
            $lixtoken = $customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)->getValue();
            $logger->info(print_r($lixtoken, true) . ' '. UserLixToken::LIX_TOKEN_ATTR_CODE);
        }
        return $lixtoken;
    }
    public function verifyScratch(){
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/VerifyPredict.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        // $logger->info("In Here");

        $lixtoken = $this->getUserToken();
        if($lixtoken){
            $params = $this->getRequest()->getParams();
            $logger->info(print_r($params, true) . ' params');
            $taskId = key($params);
            $logger->info(print_r($taskId, true) . ' taskId');
            $storeUrl = $this->_url->getBaseUrl();
            $logger->info(print_r($storeUrl, true) . ' storeUrl');
            $apiTaskLink = $storeUrl . 'get/' . $taskId;
            $logger->info(print_r($apiTaskLink, true) . ' apiTaskLink');

            try {
                $apiResponse = $this->apiHelper->verifyScratchLink($apiTaskLink, $lixtoken);
                $logger->info("API Response: " . print_r($apiResponse, true));

                if ($apiResponse && isset($apiResponse['data'])) {
                    $points = $apiResponse['data']['link']['task']['coins_per_action'];
                    $this->setData('points', $points);
                    $this->setData('success', true);
                    return $points;
                }
            } catch (\Exception $e) {
                $this->messageManager->addError(__($e->getMessage()));
            }
            $this->setData('success', false);
            return 0;
        }
    }

    public function getMediaUrl(){
        $mediaUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA );
        return $mediaUrl;
    }

    public function getPredictListMatches() {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/predictlog.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        // $logger->info('form Block');

        $customerId = $this->_customerSession->getCustomer()->getId();
        $customerEmail = $this->_customerSession->getCustomer()->getEmail();
        $logger->info(print_r($customerId, true) . ' customerId');
        $logger->info(print_r($customerEmail, true) . ' customerEmail');
        $customeratt = $this->_customerRepositoryInterface->getById($customerId);

        $lixwallet = null;
        if($customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)) {
            $lixwallet = $customeratt->getCustomAttribute('lix_wallet')->getValue();
            $logger->info(print_r($lixwallet, true) . ' lixwallet');
        }

        $lixwalletid = $this->apiHelper->getLixWalletId($lixwallet);
        $logger->info(print_r($lixwalletid , true).' wallet id');
	    //$lixppwalletid = $this->apiHelper->getLixcaWalletId();
	    //$logger->info(print_r($lixppwalletid, true) . ' $lixppwalletid');
	    //$lixxwalletid = $this->apiHelper->getLixxWalletId();
        //$logger->info(print_r($lixxwalletid, true) . ' $lixxwalletid');

        $response = $this->apiHelper->getPredictListMatch($lixwalletid);


        // Check if the response contains prediction tasks
        if (isset($response['prediction_tasks'])) {
            // Get current UTC timestamp
            $currentTimestamp = time();

            // Filter and sort the tasks by match_date
            $response['prediction_tasks'] = array_filter($response['prediction_tasks'], function ($task) use ($currentTimestamp) {
                $taskTimestamp = strtotime($task['match_date']);
                return $taskTimestamp >= $currentTimestamp || $task['match_status'] !== 'finished';
            });

            usort($response['prediction_tasks'], function ($a, $b) {
                return strtotime($b['match_date']) - strtotime($a['match_date']);
            });

           // Sort the prediction tasks by match_date in ascending order
           // usort($response['prediction_tasks'], function($a, $b) {
               // return strtotime($a['match_date']) - strtotime($b['match_date']);
           // });


        } else {
            $response['prediction_tasks'] = [];
        }

        return $response['prediction_tasks'];
    }

    public function getPredictListUsers() {
        // Get the current user's ID
        //$userId = $this->_customerSession->getCustomer()->getId();
        //$logger->info(print_r($userId, true) . ' userId');

	    $customerId = $this->_customerSession->getCustomer()->getId();
	    $customerEmail = $this->_customerSession->getCustomer()->getEmail();

        $customeratt = $this->_customerRepositoryInterface->getById($customerId);
        $lixtoken = null;
        $lixwallet = null;
        if($customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)) {
            $lixtoken = $customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)->getValue();
            $lixwallet = $customeratt->getCustomAttribute('lix_wallet')->getValue();
        }

        $lixwalletid = $this->apiHelper->getLixWalletId($lixwallet);

	    // Get the API response
        $response = $this->apiHelper->getPredictListUser($lixtoken, $lixwalletid);

        // Return the filtered predictions
        return $response;
    }

    public function displayScratchCards(){
        $orgId = $this->dataHelper->getOrganisationId();
        $taskList = $this->dataHelper->getAllScrachTaskList($orgId);

        return $taskList;
    }
}

