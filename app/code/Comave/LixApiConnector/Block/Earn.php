<?php
namespace Comave\LixApiConnector\Block;

use Magento\Framework\UrlInterface;
use Magento\Framework\App\Request\Http;
use Magento\Reward\Model\RewardFactory;
use Magento\Framework\View\Element\Template;
use Coditron\Lixtask\Model\ResourceModel\Customform\CollectionFactory as CustomformCollectionFactory;


class Earn extends \Magento\Framework\View\Element\Template
{
    /**
    *  @var \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
    */
    protected $helperData;
    protected $urlBuilder;
    protected $request;
    protected $_customerRepositoryInterface;
    protected $customformCollectionFactory;
   /**
    * Construct
    *
    * @param \Magento\Customer\Model\Session $customerSession
    * @param TravFormModel $travFormModel
    * @param array $data
    */
    public function __construct(        
        \Magento\Framework\View\Element\Template\Context $context,
        \Comave\LixApiConnector\Helper\Data $helperData,
        UrlInterface $urlBuilder,
        Http $request,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        RewardFactory $rewardFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CustomformCollectionFactory $customformCollectionFactory,
        array $data = []
    ) { 
        $this->helperData = $helperData;
        $this->urlBuilder = $urlBuilder;
        $this->request = $request;
        $this->_customerSession = $customerSession;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_rewardFactory = $rewardFactory;
        $this->_storeManager = $storeManager;
        $this->customformCollectionFactory = $customformCollectionFactory;
        parent::__construct($context, $data);
    }

    public function getTaskCollection()
    {
        $collection = $this->customformCollectionFactory->create();
        $collection->addFieldToSelect(['task_id', 'task_title','image','description','task_button_label','custom_currency_organisation_id']);
        
        // Add filter based on custom_currency_organisation_id
        $custwallet = $this->customerWallet();
        if ($custwallet == 'LIXCA') {
            $collection->addFieldToFilter('custom_currency_organisation_id', 'LIXCA');
        }else if ($custwallet == 'LIXX') {
            $collection->addFieldToFilter('custom_currency_organisation_id', 'LIXX');
        }
        return $collection;
    }

    public function taskCollection()
    {
        $taskCollection = $this->getTaskCollection();
        return $taskCollection;
       
    }

    public function storeUrl(){
        return $this->_storeManager->getStore()->getBaseUrl();
    }

    public function customerWallet(){
        $customerId = $this->_customerSession->getCustomer()->getId();
        $customer = $this->_customerRepositoryInterface->getById($customerId);
        $websiteId = $customer->getWebsiteId();
        $email = $customer->getEmail();
        $lixwallet = $customer->getCustomAttribute('lix_wallet');
        if ($lixwallet) {
            $value = $lixwallet->getValue();
            return $value;
        }
    }
}