<?php
namespace Comave\LixApiConnector\Block\Scratchcard;

use Comave\LixApiConnector\Model\Service\UserLixToken;
use Magento\Framework\View\Element\Template;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\Request\Http;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Message\ManagerInterface;



class View extends Template
{
    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\View\Element\Template\Context $context,
        UrlInterface $url,
        Http $request,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        ResourceConnection $resourceConnection,
        \Comave\LixApiConnector\Helper\Data $apiHelper,
        \Magento\Framework\Session\SessionManagerInterface $coreSession,
        ResultFactory $resultFactory,
        JsonFactory $resultJsonFactory,
        PageFactory $resultPageFactory,
        \Coditron\Lixtask\Helper\Api $dataHelper,
        UrlInterface $urlBuilder,
        ManagerInterface $messageManager,
        array $data = []
    ) {
        $this->_storeManager = $storeManager;
        $this->_url = $url;
        $this->request = $request;
        $this->_customerSession = $customerSession;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_resourceConnection = $resourceConnection;
        $this->apiHelper = $apiHelper;
        $this->resultFactory = $resultFactory;
        $this->coreSession = $coreSession;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->_resultPageFactory = $resultPageFactory;
        $this->dataHelper = $dataHelper;
         $this->urlBuilder = $urlBuilder;
        $this->messageManager = $messageManager;
        parent::__construct($context, $data);
    }
    protected function _prepareLayout(){
        return parent::_prepareLayout();
    }

    public function getUserToken(){

        $lixtoken = '';
        $customerId = $this->_customerSession->getCustomer()->getId();
        $customeratt = $this->_customerRepositoryInterface->getById($customerId);
        if($customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)) {
            $lixtoken = $customeratt->getCustomAttribute(UserLixToken::LIX_TOKEN_ATTR_CODE)->getValue();
        }
        return $lixtoken;
    }
    public function verifyScratch(){

        $lixtoken = $this->getUserToken();
        if($lixtoken){
            $params = $this->getRequest()->getParams();
            $taskId = key($params);
            $storeUrl = $this->_url->getBaseUrl();
            $apiTaskLink = $storeUrl . 'get/' . $taskId;
            try {
                $apiResponse = $this->apiHelper->verifyScratchLink($apiTaskLink, $lixtoken);
                return $apiResponse;
            } catch (\Exception $e) {
                $this->messageManager->addError(__($e->getMessage()));
            }
        }
    }
    public function scratchActivity(){

        $customer = $this->_customerSession->getCustomer();
        $customerEmail = $customer->getEmail();
        $customerName = $customer->getFirstname();
        $customerPhone = $customer->getPhoneNo();

        $bearer_token = $this->getUserToken();
        if($bearer_token){
            $params = $this->getRequest()->getParams();
            $taskId = key($params);

            $activityData = array();
            $activityData['phone'] = $customerPhone;
            $activityData['email'] = $customerEmail;
            $postActivityData = json_encode($activityData);
            try {
                $apiResponse = $this->apiHelper->submitScratchActivity($bearer_token, $taskId, $postActivityData);

                if ($apiResponse['success'] == true) {
                    $points = $apiResponse['data']['coins_earned'];
                    return $points;
                } else {
                    $message = isset($apiResponse['message']) ? $apiResponse['message'] : 'Unknown error occurred.';
                    return $message;
                }
            } catch (\Exception $e) {
                $this->messageManager->addError(__($e->getMessage()));
                $this->setData('success', false);
                $this->setData('error_message', $e->getMessage());
                return $e->getMessage();
            }
        } else {
            $message = 'Bearer token not found.';
            $this->setData('success', false);
            $this->setData('error_message', $message);
            return $message;
        }

    }

    public function getMediaUrl()
    {
         $mediaUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA );
         return $mediaUrl;
    }

    public function displayScratchCards(){
        $orgId = $this->dataHelper->getOrganisationId();
        $lixtoken = $this->getUserToken();

        // Fetch scratch cards with status "VIEWED"
        $viewedTaskList = $this->apiHelper->getscratchCardsList('VIEWED', $lixtoken);

        // Fetch scratch cards with status "USED"
        $usedTaskList = $this->apiHelper->getscratchCardsList('USED', $lixtoken);

        return [
            'viewed' => $viewedTaskList,
            'used' => $usedTaskList,
        ];
    }

	   public function getViewedScratchCards() {

	    $orgId = $this->dataHelper->getOrganisationId();
	    $lixtoken = $this->getUserToken();

	    // Fetch scratch cards with status "VIEWED"
	    $viewedTaskList = $this->apiHelper->getscratchCardsList('VIEWED', $lixtoken);


	    return $viewedTaskList;
	}


    public function getPointsActionUrl()
    {
        return  $this->_url->getUrl('lixreward/scratchcard/submit');
    }

      public function getMyRewardUrl()
    {
        return $this->urlBuilder->getUrl('lixreward/scratchcard/myreward');
    }

    public function getViewUrl()
    {
        return $this->urlBuilder->getUrl('lixreward/scratchcard/get');
    }

    public function getRegLIXXId()
    {
        $lixxRegId = $this->apiHelper->getRegScrtchLixxId();
        return $lixxRegId;
    }

}

